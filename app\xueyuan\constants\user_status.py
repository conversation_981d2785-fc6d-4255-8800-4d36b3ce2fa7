# coding: utf-8
"""
用户状态常量定义

该模块定义了用户状态的所有可能值，确保整个应用中状态值的一致性。

作者: 小帅工具箱
版本: v1.0
"""

from enum import Enum


class UserStatus(Enum):
    """用户状态枚举"""
    NOT_STARTED = "未开始"      # 用户尚未开始学习
    STUDYING = "学习中"         # 用户正在学习
    COMPLETED = "已完成"        # 用户学习已完成
    FAILED = "学习失败"         # 用户学习失败
    ERROR = "错误"             # 用户状态异常


class CompleteStatus(Enum):
    """完成状态枚举"""
    COMPLETED = "0"    # 已完成
    NOT_COMPLETED = "1"  # 未完成


# 可以进行学习的用户状态
LEARNABLE_STATUSES = [UserStatus.NOT_STARTED.value, UserStatus.FAILED.value]

# 状态显示映射
STATUS_DISPLAY_MAP = {
    UserStatus.NOT_STARTED.value: "未开始",
    UserStatus.STUDYING.value: "学习中", 
    UserStatus.COMPLETED.value: "已完成",
    UserStatus.FAILED.value: "学习失败",
    UserStatus.ERROR.value: "错误"
}

# 状态颜色映射（用于UI显示）
STATUS_COLOR_MAP = {
    UserStatus.NOT_STARTED.value: "#666666",    # 灰色
    UserStatus.STUDYING.value: "#1890ff",       # 蓝色
    UserStatus.COMPLETED.value: "#52c41a",      # 绿色
    UserStatus.FAILED.value: "#ff4d4f",         # 红色
    UserStatus.ERROR.value: "#fa8c16"           # 橙色
}


def is_learnable_status(status: str, complete_status: str = None) -> bool:
    """
    判断用户状态是否可以进行学习

    Args:
        status: 用户状态
        complete_status: 完成状态（可选）

    Returns:
        bool: 是否可以学习
    """
    # 如果提供了完成状态，优先考虑完成状态
    if complete_status is not None:
        # 未完成的用户可以学习，已完成的用户不能学习
        if complete_status == CompleteStatus.NOT_COMPLETED.value:  # "1" 表示未完成
            return True
        elif complete_status == CompleteStatus.COMPLETED.value:    # "0" 表示已完成
            return False

    # 如果没有完成状态信息，则根据学习状态判断
    return status in LEARNABLE_STATUSES


def get_status_display(status: str) -> str:
    """
    获取状态的显示文本
    
    Args:
        status: 用户状态
        
    Returns:
        str: 显示文本
    """
    return STATUS_DISPLAY_MAP.get(status, status)


def get_status_color(status: str) -> str:
    """
    获取状态的颜色
    
    Args:
        status: 用户状态
        
    Returns:
        str: 颜色值
    """
    return STATUS_COLOR_MAP.get(status, "#666666")
