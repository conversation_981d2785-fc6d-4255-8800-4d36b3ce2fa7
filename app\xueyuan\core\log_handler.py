# coding: utf-8
"""
全局日志处理器模块

该模块定义了全局日志处理器，负责接收各个组件的日志信号并写入数据库。

主要功能：
- 接收学习引擎的日志信号
- 接收线程池的日志信号
- 将日志写入数据库
- 提供统一的日志管理

类说明：
- GlobalLogHandler: 全局日志处理器

作者: 小帅工具箱
版本: v1.0
"""

from PySide6.QtCore import QObject, Signal, QThread
from typing import Optional

from ..database.dao import log_dao
from ..database.models import LogEntry


class GlobalLogHandler(QObject):
    """
    全局日志处理器
    
    负责接收各个组件的日志信号并统一处理，包括写入数据库、
    控制台输出等功能。
    """
    
    # 信号定义
    logReceived = Signal(str, str, str, str)  # 级别, 消息, 模块, 用户手机号
    
    def __init__(self, parent=None):
        """
        初始化全局日志处理器
        
        Args:
            parent: 父对象
        """
        super().__init__(parent)
        
        # 连接信号到处理函数
        self.logReceived.connect(self._handle_log_message)
    
    def log_message(self, level: str, message: str, module: str = "", user_phone: str = ""):
        """
        记录日志消息
        
        Args:
            level: 日志级别
            message: 日志消息
            module: 模块名称
            user_phone: 用户手机号
        """
        # 发送信号到主线程处理
        self.logReceived.emit(level, message, module, user_phone)
    
    def _handle_log_message(self, level: str, message: str, module: str, user_phone: str):
        """
        处理日志消息
        
        Args:
            level: 日志级别
            message: 日志消息
            module: 模块名称
            user_phone: 用户手机号
        """
        try:
            # 创建日志条目
            log_entry = LogEntry(
                level=level,
                message=message,
                module=module,
                user_phone=user_phone
            )
            
            # 写入数据库
            success = log_dao.create_log(log_entry)
            if not success:
                print(f"写入日志到数据库失败: {message}")
            
            # 控制台输出
            print(f"[{level}] [{module}] [{user_phone}] {message}")
            
        except Exception as e:
            print(f"处理日志消息失败: {str(e)}")
    
    def connect_engine_signals(self, engine):
        """
        连接学习引擎的信号
        
        Args:
            engine: 学习引擎实例
        """
        try:
            if hasattr(engine, 'logMessage'):
                engine.logMessage.connect(
                    lambda level, message: self.log_message(level, message, "StudyEngine", "")
                )
            
            if hasattr(engine, 'errorOccurred'):
                engine.errorOccurred.connect(
                    lambda error_type, message: self.log_message("ERROR", f"{error_type}: {message}", "StudyEngine", "")
                )
                
        except Exception as e:
            print(f"连接学习引擎信号失败: {str(e)}")
    
    def connect_thread_pool_signals(self, thread_pool):
        """
        连接线程池的信号
        
        Args:
            thread_pool: 线程池实例
        """
        try:
            if hasattr(thread_pool, 'logMessage'):
                thread_pool.logMessage.connect(
                    lambda level, message: self.log_message(level, message, "ThreadPool", "")
                )
            
            if hasattr(thread_pool, 'taskStarted'):
                thread_pool.taskStarted.connect(
                    lambda task_id, user_phone: self.log_message("INFO", f"任务开始: {task_id}", "ThreadPool", user_phone)
                )
            
            if hasattr(thread_pool, 'taskCompleted'):
                thread_pool.taskCompleted.connect(
                    lambda task_id, user_phone, success: self.log_message(
                        "INFO" if success else "WARNING", 
                        f"任务{'完成' if success else '失败'}: {task_id}", 
                        "ThreadPool", 
                        user_phone
                    )
                )
            
            if hasattr(thread_pool, 'taskFailed'):
                thread_pool.taskFailed.connect(
                    lambda task_id, user_phone, error_msg: self.log_message(
                        "ERROR", 
                        f"任务失败: {task_id} - {error_msg}", 
                        "ThreadPool", 
                        user_phone
                    )
                )
                
        except Exception as e:
            print(f"连接线程池信号失败: {str(e)}")


# 全局日志处理器实例
global_log_handler = GlobalLogHandler()
