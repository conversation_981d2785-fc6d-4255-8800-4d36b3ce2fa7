# coding: utf-8
"""
自动化学习引擎模块

该模块实现了基于Playwright的自动化学习引擎，提供登录、状态检查、课程学习等核心功能。

主要功能：
- 自动登录学习平台
- 获取用户学习状态
- 获取课程列表
- 自动学习课程
- 视频播放监控

类说明：
- StudyEngine: 自动化学习引擎

作者: 小帅工具箱
版本: v1.0
"""

import asyncio
import time
from typing import Dict, Any, List, Optional
from abc import ABC, abstractmethod, ABCMeta

from PySide6.QtCore import QObject, Signal

try:
    from playwright.async_api import async_playwright, <PERSON>rowser, BrowserContext, Page
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    async_playwright = None

from .api_capture import APICapture
from .course_manager import CourseManager
from .video_monitor import VideoLearningMonitor
from ..common.config import cfg
from ..database.dao import user_dao, course_dao
from ...ocr import OCRManager


class IStudyEngine(ABC):
    """学习引擎抽象接口"""
    
    @abstractmethod
    async def login(self, credentials: Dict[str, str]) -> bool:
        """登录接口"""
        pass
    
    @abstractmethod
    async def get_study_status(self) -> Dict[str, Any]:
        """获取学习状态"""
        pass
    
    @abstractmethod
    async def start_study(self, course_id: str) -> bool:
        """开始学习"""
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """清理资源"""
        pass


class QObjectMeta(type(QObject), ABCMeta):
    """解决QObject和ABC元类冲突的元类"""
    pass


class StudyEngine(QObject, IStudyEngine, metaclass=QObjectMeta):
    """
    自动化学习引擎
    
    基于Playwright实现的学习平台自动化引擎，支持自动登录、课程学习等功能。
    """
    
    # 信号定义
    loginStatusChanged = Signal(bool, str)  # 登录状态, 消息
    studyProgressChanged = Signal(str, int, int)  # 课程ID, 当前进度, 总进度
    errorOccurred = Signal(str, str)  # 错误类型, 错误消息
    logMessage = Signal(str, str)  # 日志级别, 消息内容
    
    def __init__(self, parent=None):
        """初始化学习引擎"""
        super().__init__(parent)
        
        # Playwright相关
        self.playwright = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        
        # 组件
        self.api_capture = APICapture(self)
        self.ocr_manager = OCRManager()
        self.course_manager = None  # 延迟初始化
        self.video_monitor = None  # 延迟初始化
        
        # 状态管理
        self.is_initialized = False
        self.is_logged_in = False
        self.current_user = {}
        self.study_status = {}

        # 浏览器类型映射（用户配置 -> Playwright API）
        self.browser_type_mapping = {
            'chrome': 'chromium',  # Chrome 使用 chromium 引擎
            'firefox': 'firefox',
            'edge': 'chromium',    # Edge 使用 Chromium 内核
            'webkit': 'webkit'
        }

        # 浏览器通道映射（用于指定具体浏览器）
        self.browser_channel_mapping = {
            'chrome': 'chrome',     # 使用系统安装的 Chrome
            'edge': 'msedge',       # 使用系统安装的 Edge
        }

        # 配置参数
        browser_type_config = cfg.browserType.value.lower()
        playwright_browser_type = self.browser_type_mapping.get(browser_type_config, 'chromium')

        self.config = {
            'browser_type': playwright_browser_type,
            'browser_channel': self.browser_channel_mapping.get(browser_type_config),
            'original_browser_type': browser_type_config,
            'headless': cfg.headless.value,
            'disable_images': cfg.disableImages.value,
            'user_agent': cfg.userAgent.value,
            'timeout': cfg.pageLoadTimeout.value * 1000,  # 转换为毫秒
            'retry_count': cfg.retryCount.value,
            'delay_time': cfg.delayTime.value
        }
        
        # 学习平台URL
        self.base_url = "https://study.jxgbwlxy.gov.cn"
        self.login_url = f"{self.base_url}/index"
        
        # 连接API捕获信号
        self.api_capture.dataReceived.connect(self._on_api_data_received)
        self.api_capture.errorOccurred.connect(self._on_api_error)
    
    async def initialize(self) -> bool:
        """
        初始化学习引擎
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            if not PLAYWRIGHT_AVAILABLE:
                raise Exception("Playwright未安装，请先安装playwright库")
            
            # 初始化OCR管理器
            ocr_config = {
                'primary_engine': cfg.primaryEngine.value,
                'fallback_engine': cfg.fallbackEngine.value,
                'confidence_threshold': cfg.confidenceThreshold.value,
                'engines': {
                    'ddddocr': {},
                    'baidu': {
                        'api_key': cfg.baiduApiKey.value,
                        'secret_key': cfg.baiduSecretKey.value
                    }
                }
            }
            await self.ocr_manager.initialize(ocr_config)
            
            # 初始化Playwright
            self.playwright = await async_playwright().start()
            
            # 创建浏览器
            browser_type = getattr(self.playwright, self.config['browser_type'])

            # 配置启动选项
            launch_options = {
                'headless': self.config['headless'],
                'args': ['--no-sandbox', '--disable-dev-shm-usage'] if self.config['headless'] else []
            }

            # 如果有指定的浏览器通道，使用 channel 参数
            if self.config['browser_channel']:
                launch_options['channel'] = self.config['browser_channel']
                self.logMessage.emit("INFO", f"使用浏览器通道: {self.config['browser_channel']}")
            else:
                self.logMessage.emit("INFO", f"使用默认 {self.config['browser_type']} 浏览器")

            try:
                self.browser = await browser_type.launch(**launch_options)
            except Exception as e:
                # 如果使用 channel 失败，回退到默认方式
                if 'channel' in launch_options:
                    self.logMessage.emit("WARNING", f"使用通道 {launch_options['channel']} 启动失败，回退到默认方式: {str(e)}")
                    del launch_options['channel']
                    self.browser = await browser_type.launch(**launch_options)
                else:
                    raise e
            
            # 创建浏览器上下文
            context_options = {
                'viewport': {'width': 1920, 'height': 1080},
                'user_agent': self.config['user_agent'] if self.config['user_agent'] else None
            }
            
            if self.config['disable_images']:
                context_options['bypass_csp'] = True
            
            self.context = await self.browser.new_context(**context_options)
            
            # 创建页面
            self.page = await self.context.new_page()
            
            # 设置页面超时
            self.page.set_default_timeout(self.config['timeout'])
            
            # 监听响应事件
            self.page.on('response', self._handle_response)

            # 初始化课程管理器和视频监控器
            self.course_manager = CourseManager(self, self)
            self.video_monitor = VideoLearningMonitor(self, self)

            # 连接信号
            self.course_manager.logMessage.connect(self.logMessage)
            self.course_manager.errorOccurred.connect(self.errorOccurred)
            self.video_monitor.logMessage.connect(self.logMessage)
            self.video_monitor.errorOccurred.connect(self.errorOccurred)

            self.is_initialized = True
            self.logMessage.emit("INFO", "学习引擎初始化成功")
            return True
            
        except Exception as e:
            error_msg = f"学习引擎初始化失败: {str(e)}"
            self.errorOccurred.emit("INIT_ERROR", error_msg)
            self.logMessage.emit("ERROR", error_msg)
            return False
    
    async def _handle_response(self, response) -> None:
        """处理页面响应"""
        try:
            await self.api_capture.handle_response(response)
        except Exception as e:
            self.logMessage.emit("WARNING", f"处理响应失败: {str(e)}")
    
    def _on_api_data_received(self, url: str, data: Dict[str, Any]) -> None:
        """API数据接收回调"""
        self.logMessage.emit("DEBUG", f"接收到API数据: {url}")
        
        # 根据API类型更新相应的状态
        if "myData/online" in url:
            self._update_study_status(data)
        elif "studentArchives/student" in url:
            self._update_user_info(data)
    
    def _on_api_error(self, url: str, error: str) -> None:
        """API错误回调"""
        self.logMessage.emit("WARNING", f"API错误 {url}: {error}")
    
    def _update_study_status(self, data: Dict[str, Any]) -> None:
        """更新学习状态"""
        try:
            if 'data' in data:
                self.study_status = data['data']
                self.logMessage.emit("DEBUG", "学习状态已更新")
        except Exception as e:
            self.logMessage.emit("WARNING", f"更新学习状态失败: {str(e)}")
    
    def _update_user_info(self, data: Dict[str, Any]) -> None:
        """更新用户信息"""
        try:
            if 'data' in data:
                self.current_user = data['data']
                self.logMessage.emit("DEBUG", "用户信息已更新")
        except Exception as e:
            self.logMessage.emit("WARNING", f"更新用户信息失败: {str(e)}")
    
    async def login(self, credentials: Dict[str, str]) -> bool:
        """
        登录学习平台
        
        Args:
            credentials: 登录凭据，包含phone和password
            
        Returns:
            bool: 登录是否成功
        """
        if not self.is_initialized:
            await self.initialize()
        
        try:
            phone = credentials.get('phone', '')
            password = credentials.get('password', '')
            
            if not phone or not password:
                raise Exception("手机号和密码不能为空")
            
            self.logMessage.emit("INFO", f"开始登录用户: {phone}")
            
            # 访问登录页面
            await self.page.goto(self.login_url)
            await self.page.wait_for_load_state("networkidle")
            
            # 填写手机号
            await self.page.fill('.el-input__inner[placeholder="您的手机号"]', phone)
            await asyncio.sleep(self.config['delay_time'])

            # 填写密码
            await self.page.fill('.el-input__inner[placeholder="请输入密码"]', password)
            await asyncio.sleep(self.config['delay_time'])
            
            # 处理验证码（如果存在）
            captcha_success = await self._handle_captcha()
            if not captcha_success:
                raise Exception("验证码处理失败")
            
            # 提交登录
            await self.page.click('.loginBtn.el-button')
            
            # 等待登录结果
            try:
                await self.page.wait_for_url("**/study/data", timeout=30000)
                
                # 等待API数据捕获
                await self.page.wait_for_load_state("networkidle")
                await asyncio.sleep(3)
                
                self.is_logged_in = True
                self.loginStatusChanged.emit(True, "登录成功")
                self.logMessage.emit("INFO", f"用户 {phone} 登录成功")
                return True
                
            except Exception:
                # 检查是否有错误提示
                error_elements = await self.page.query_selector_all('.el-message--error')
                if error_elements:
                    error_text = await error_elements[0].text_content()
                    raise Exception(f"登录失败: {error_text}")
                else:
                    raise Exception("登录超时或失败")
                    
        except Exception as e:
            error_msg = f"登录失败: {str(e)}"
            self.is_logged_in = False
            self.loginStatusChanged.emit(False, error_msg)
            self.errorOccurred.emit("LOGIN_ERROR", error_msg)
            self.logMessage.emit("ERROR", error_msg)
            return False
    
    async def _handle_captcha(self) -> bool:
        """
        处理验证码（严格按照设计文档）

        Returns:
            bool: 验证码处理是否成功
        """
        max_retries = 3

        for attempt in range(max_retries):
            try:
                # 查找验证码图片
                captcha_img = await self.page.query_selector('.yzmImg')

                if not captcha_img:
                    # 没有验证码，直接返回成功
                    self.logMessage.emit("INFO", "未检测到验证码，跳过验证码处理")
                    return True

                # 获取验证码图片
                img_data = await captcha_img.screenshot()
                self.logMessage.emit("DEBUG", f"验证码图片大小: {len(img_data)} bytes")

                # 使用OCR识别验证码
                result = await self.ocr_manager.recognize_captcha(img_data)

                if result.is_valid(self.ocr_manager.confidence_threshold):
                    # 填写验证码（按设计文档使用指定选择器）
                    await self.page.fill('.el-input__inner[placeholder="验证码"]', result.text)
                    self.logMessage.emit("INFO", f"验证码识别成功: {result.text} (置信度: {result.confidence:.2f})")
                    return True
                else:
                    self.logMessage.emit("WARNING", f"验证码识别置信度过低: {result.confidence:.2f}, 文本: {result.text}")

                    # 如果不是最后一次尝试，刷新验证码（按设计文档点击 .yzmImg）
                    if attempt < max_retries - 1:
                        self.logMessage.emit("INFO", f"第 {attempt + 1} 次验证码识别失败，刷新验证码重试")
                        await self.page.click('.yzmImg')
                        await asyncio.sleep(1)

            except Exception as e:
                self.logMessage.emit("ERROR", f"验证码处理失败: {str(e)}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(1)
                    continue

        return False
    
    async def get_study_status(self) -> Dict[str, Any]:
        """
        获取学习状态
        
        Returns:
            Dict[str, Any]: 学习状态数据
        """
        if not self.is_logged_in:
            return {}
        
        # 从API捕获器获取数据
        status_data = self.api_capture.get_user_status_data()
        student_data = self.api_capture.get_student_info_data()
        
        return {
            'user_status': status_data,
            'student_info': student_data,
            'timestamp': time.time()
        }
    
    async def start_study(self, course_id: str) -> bool:
        """
        开始学习指定课程

        Args:
            course_id: 课程ID

        Returns:
            bool: 是否成功开始学习
        """
        # 这里是学习逻辑的占位符，后续会详细实现
        self.logMessage.emit("INFO", f"开始学习课程: {course_id}")
        return True

    async def start_learning_process(self, phone: str, password: str) -> bool:
        """
        开始完整的学习流程

        Args:
            phone: 用户手机号
            password: 用户密码

        Returns:
            bool: 学习流程是否成功完成
        """
        try:
            self.logMessage.emit("INFO", f"开始用户 {phone} 的学习流程")

            # 1. 登录
            login_success = await self.login({'phone': phone, 'password': password})
            if not login_success:
                self.logMessage.emit("ERROR", f"用户 {phone} 登录失败")
                return False

            # 2. 检查学习状态
            if await self.check_study_completion():
                self.logMessage.emit("INFO", f"用户 {phone} 学习已完成")
                return True

            # 3. 获取课程数据
            courses_data = await self.course_manager.load_or_fetch_courses(phone)
            if not courses_data:
                self.logMessage.emit("ERROR", f"获取用户 {phone} 的课程数据失败")
                return False

            # 4. 开始学习未完成的课程
            incomplete_courses = self.course_manager.get_incomplete_courses(courses_data)
            if not incomplete_courses:
                self.logMessage.emit("INFO", f"用户 {phone} 没有未完成的课程")
                return True

            self.logMessage.emit("INFO", f"用户 {phone} 有 {len(incomplete_courses)} 门未完成的课程")

            for course in incomplete_courses:
                course_name = course.get('name', '')
                self.logMessage.emit("INFO", f"开始学习课程: {course_name}")

                # 使用视频监控器学习课程
                success = await self.video_monitor.start_course_learning(course)
                if success:
                    self.logMessage.emit("INFO", f"课程 {course_name} 学习完成")
                else:
                    self.logMessage.emit("WARNING", f"课程 {course_name} 学习失败")

                # 添加延迟避免过快操作
                await asyncio.sleep(self.config['delay_time'])

            # 5. 最终状态检查
            final_completion = await self.check_study_completion()
            if final_completion:
                self.logMessage.emit("INFO", f"用户 {phone} 所有课程学习完成")
            else:
                self.logMessage.emit("WARNING", f"用户 {phone} 仍有未完成的课程")

            return final_completion

        except Exception as e:
            error_msg = f"学习流程执行失败: {str(e)}"
            self.errorOccurred.emit("LEARNING_PROCESS_ERROR", error_msg)
            self.logMessage.emit("ERROR", error_msg)
            return False

    async def check_study_completion(self) -> bool:
        """
        检查学习是否完成

        Returns:
            bool: 学习是否已完成
        """
        try:
            # 更新用户状态
            user_status = await self.update_user_status_from_api()

            # 检查完成状态
            complete_status = user_status.get('user_status', {}).get('completeStatus', '1')
            return complete_status == '0'  # 0表示已完成，1表示未完成

        except Exception as e:
            self.logMessage.emit("WARNING", f"检查学习完成状态失败: {str(e)}")
            return False

    async def get_course_data_for_user(self, phone: str) -> Dict[str, List[Dict[str, Any]]]:
        """
        获取用户的课程数据

        Args:
            phone: 用户手机号

        Returns:
            Dict[str, List[Dict[str, Any]]]: 课程数据
        """
        if not self.course_manager:
            self.logMessage.emit("ERROR", "课程管理器未初始化")
            return {"必修课": [], "选修课": []}

        return await self.course_manager.load_or_fetch_courses(phone)
    
    async def cleanup(self) -> None:
        """清理资源"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
            
            await self.ocr_manager.cleanup()
            
            self.is_initialized = False
            self.is_logged_in = False
            self.logMessage.emit("INFO", "学习引擎资源清理完成")
            
        except Exception as e:
            self.logMessage.emit("ERROR", f"清理资源失败: {str(e)}")
    
    def get_engine_status(self) -> Dict[str, Any]:
        """
        获取引擎状态
        
        Returns:
            Dict[str, Any]: 引擎状态信息
        """
        return {
            'initialized': self.is_initialized,
            'logged_in': self.is_logged_in,
            'current_user': self.current_user,
            'study_status': self.study_status,
            'api_stats': self.api_capture.get_stats(),
            'ocr_stats': self.ocr_manager.get_stats() if self.ocr_manager else {}
        }
