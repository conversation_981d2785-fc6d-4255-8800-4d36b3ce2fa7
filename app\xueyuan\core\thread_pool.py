# coding: utf-8
"""
学习线程池管理模块

该模块实现了多用户并发学习、任务队列管理和异常处理重试机制。

主要功能：
- 多用户并发学习任务管理
- 任务队列和优先级管理
- 异常处理和自动重试
- 任务状态监控和统计
- 资源管理和清理

类说明：
- StudyTask: 学习任务
- StudyThreadPool: 学习线程池管理器

作者: 小帅工具箱
版本: v1.0
"""

import asyncio
import time
import uuid
from concurrent.futures import ThreadPoolExecutor, Future
from typing import Dict, Any, List, Optional, Callable
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime

from PySide6.QtCore import QObject, Signal, QTimer

from .study_engine import StudyEngine
from ..common.config import cfg
from ..database.dao import user_dao


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 等待中
    RUNNING = "running"      # 运行中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"        # 失败
    CANCELLED = "cancelled"  # 已取消
    RETRYING = "retrying"    # 重试中


class TaskPriority(Enum):
    """任务优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


@dataclass
class StudyTask:
    """学习任务数据类"""
    task_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    user_phone: str = ""
    user_password: str = ""
    priority: TaskPriority = TaskPriority.NORMAL
    status: TaskStatus = TaskStatus.PENDING
    created_time: float = field(default_factory=time.time)
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    retry_count: int = 0
    max_retries: int = 3
    error_message: str = ""
    progress: float = 0.0
    result: Optional[Dict[str, Any]] = None
    callback: Optional[Callable] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'task_id': self.task_id,
            'user_phone': self.user_phone,
            'priority': self.priority.name,
            'status': self.status.name,
            'created_time': self.created_time,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'retry_count': self.retry_count,
            'max_retries': self.max_retries,
            'error_message': self.error_message,
            'progress': self.progress,
            'duration': (self.end_time - self.start_time) if self.start_time and self.end_time else None
        }


class StudyThreadPool(QObject):
    """
    学习线程池管理器
    
    负责管理多用户并发学习任务，提供任务队列管理、异常处理和重试机制。
    """
    
    # 信号定义
    taskAdded = Signal(str)  # 任务ID
    taskStarted = Signal(str, str)  # 任务ID, 用户手机号
    taskCompleted = Signal(str, str, bool)  # 任务ID, 用户手机号, 是否成功
    taskFailed = Signal(str, str, str)  # 任务ID, 用户手机号, 错误消息
    taskProgressChanged = Signal(str, str, float)  # 任务ID, 用户手机号, 进度
    poolStatusChanged = Signal(dict)  # 线程池状态
    errorOccurred = Signal(str, str)  # 错误类型, 错误消息
    logMessage = Signal(str, str)  # 日志级别, 消息内容
    
    def __init__(self, max_workers: Optional[int] = None, parent=None):
        """
        初始化线程池管理器
        
        Args:
            max_workers: 最大工作线程数
            parent: 父对象
        """
        super().__init__(parent)
        
        # 线程池配置
        self.max_workers = max_workers or cfg.concurrentCount.value
        self.executor = ThreadPoolExecutor(max_workers=self.max_workers)
        
        # 任务管理
        self.tasks: Dict[str, StudyTask] = {}
        self.running_tasks: Dict[str, Future] = {}
        self.task_queue: List[StudyTask] = []
        
        # 统计信息
        self.stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'running_tasks': 0,
            'pending_tasks': 0,
            'start_time': time.time()
        }
        
        # 定时器用于状态更新 - 移到主线程创建
        self.status_timer = None
        self._init_status_timer()
        
        self.logMessage.emit("INFO", f"学习线程池初始化完成，最大并发数: {self.max_workers}")

    def _init_status_timer(self):
        """初始化状态定时器（在主线程中调用）"""
        try:
            from PySide6.QtCore import QTimer
            self.status_timer = QTimer()
            self.status_timer.timeout.connect(self._update_pool_status)
            self.status_timer.start(5000)  # 每5秒更新一次状态
        except Exception as e:
            self.logMessage.emit("WARNING", f"初始化状态定时器失败: {str(e)}")

    def submit_study_task(self, user_phone: str, user_password: str,
                         priority: TaskPriority = TaskPriority.NORMAL,
                         callback: Optional[Callable] = None) -> str:
        """
        提交学习任务
        
        Args:
            user_phone: 用户手机号
            user_password: 用户密码
            priority: 任务优先级
            callback: 完成回调函数
            
        Returns:
            str: 任务ID
        """
        try:
            # 检查是否已有相同用户的任务
            existing_task = self._find_user_task(user_phone)
            if existing_task and existing_task.status in [TaskStatus.PENDING, TaskStatus.RUNNING, TaskStatus.RETRYING]:
                self.logMessage.emit("WARNING", f"用户 {user_phone} 已有正在执行的任务")
                return existing_task.task_id
            
            # 创建新任务
            task = StudyTask(
                user_phone=user_phone,
                user_password=user_password,
                priority=priority,
                max_retries=cfg.retryCount.value,
                callback=callback
            )
            
            # 添加到任务列表
            self.tasks[task.task_id] = task
            self.task_queue.append(task)
            
            # 按优先级排序队列
            self.task_queue.sort(key=lambda t: t.priority.value, reverse=True)
            
            # 更新统计
            self.stats['total_tasks'] += 1
            self.stats['pending_tasks'] += 1
            
            # 发送信号
            self.taskAdded.emit(task.task_id)
            self.logMessage.emit("INFO", f"添加学习任务: {user_phone} (任务ID: {task.task_id})")
            
            # 尝试执行任务
            self._try_execute_next_task()
            
            return task.task_id
            
        except Exception as e:
            error_msg = f"提交学习任务失败: {str(e)}"
            self.errorOccurred.emit("SUBMIT_TASK_ERROR", error_msg)
            self.logMessage.emit("ERROR", error_msg)
            return ""
    
    def _find_user_task(self, user_phone: str) -> Optional[StudyTask]:
        """查找用户的任务"""
        for task in self.tasks.values():
            if task.user_phone == user_phone:
                return task
        return None
    
    def _try_execute_next_task(self):
        """尝试执行下一个任务"""
        try:
            # 检查是否有可用的工作线程
            if len(self.running_tasks) >= self.max_workers:
                return
            
            # 查找下一个待执行的任务
            next_task = None
            for task in self.task_queue:
                if task.status == TaskStatus.PENDING:
                    next_task = task
                    break
            
            if not next_task:
                return
            
            # 执行任务
            self._execute_task(next_task)
            
        except Exception as e:
            self.logMessage.emit("ERROR", f"执行下一个任务失败: {str(e)}")
    
    def _execute_task(self, task: StudyTask):
        """执行任务"""
        try:
            # 更新任务状态
            task.status = TaskStatus.RUNNING
            task.start_time = time.time()
            
            # 从队列中移除
            if task in self.task_queue:
                self.task_queue.remove(task)
            
            # 更新统计
            self.stats['pending_tasks'] -= 1
            self.stats['running_tasks'] += 1
            
            # 提交到线程池执行
            future = self.executor.submit(self._run_study_task, task)
            self.running_tasks[task.task_id] = future
            
            # 添加完成回调
            future.add_done_callback(lambda f: self._task_completed(task.task_id, f))
            
            # 发送信号
            self.taskStarted.emit(task.task_id, task.user_phone)
            self.logMessage.emit("INFO", f"开始执行学习任务: {task.user_phone}")
            
        except Exception as e:
            self._handle_task_error(task, f"执行任务失败: {str(e)}")
    
    def _run_study_task(self, task: StudyTask) -> bool:
        """
        运行学习任务

        Args:
            task: 学习任务

        Returns:
            bool: 是否成功
        """
        try:
            # 创建学习引擎实例
            engine = StudyEngine()

            # 连接学习引擎信号到全局日志处理器
            from .log_handler import global_log_handler

            # 在新的事件循环中运行异步任务
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                # 连接信号（在主线程中进行）
                global_log_handler.connect_engine_signals(engine)

                # 初始化引擎
                init_success = loop.run_until_complete(engine.initialize())
                if not init_success:
                    raise Exception("学习引擎初始化失败")

                # 记录开始学习的日志
                self.logMessage.emit("INFO", f"开始执行用户 {task.user_phone} 的学习任务")

                # 更新任务进度为开始状态
                task.progress = 10.0
                self.taskProgressChanged.emit(task.task_id, task.user_phone, task.progress)

                # 执行学习流程
                result = loop.run_until_complete(
                    engine.start_learning_process(task.user_phone, task.user_password)
                )

                # 更新任务进度为完成状态
                task.progress = 100.0 if result else 90.0
                self.taskProgressChanged.emit(task.task_id, task.user_phone, task.progress)

                # 保存结果
                task.result = {'success': result, 'timestamp': time.time()}

                # 记录完成日志
                if result:
                    self.logMessage.emit("INFO", f"用户 {task.user_phone} 学习任务完成")
                else:
                    self.logMessage.emit("WARNING", f"用户 {task.user_phone} 学习任务失败")

                return result

            finally:
                # 清理资源
                loop.run_until_complete(engine.cleanup())
                loop.close()

        except Exception as e:
            task.error_message = str(e)
            self.logMessage.emit("ERROR", f"用户 {task.user_phone} 学习任务异常: {str(e)}")
            return False
    
    def _task_completed(self, task_id: str, future: Future):
        """任务完成回调"""
        try:
            task = self.tasks.get(task_id)
            if not task:
                return
            
            # 从运行任务中移除
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
            
            # 更新统计
            self.stats['running_tasks'] -= 1
            
            try:
                # 获取任务结果
                success = future.result()
                
                if success:
                    # 任务成功
                    task.status = TaskStatus.COMPLETED
                    task.end_time = time.time()
                    task.progress = 100.0
                    
                    self.stats['completed_tasks'] += 1
                    self.taskCompleted.emit(task_id, task.user_phone, True)
                    self.logMessage.emit("INFO", f"学习任务完成: {task.user_phone}")
                    
                else:
                    # 任务失败，尝试重试
                    self._handle_task_failure(task)
                
            except Exception as e:
                # 任务执行异常
                task.error_message = str(e)
                self._handle_task_failure(task)
            
            # 调用回调函数
            if task.callback:
                try:
                    # 检查回调函数的参数签名
                    import inspect
                    sig = inspect.signature(task.callback)
                    param_count = len(sig.parameters)

                    if param_count == 1:
                        # 期望一个参数（task对象）
                        task.callback(task)
                    elif param_count == 2:
                        # 期望两个参数（可能是completed和total）
                        completed = self.stats['completed_tasks']
                        total = self.stats['total_tasks']
                        task.callback(completed, total)
                    else:
                        # 无参数或其他情况，尝试无参数调用
                        task.callback()

                except Exception as e:
                    self.logMessage.emit("WARNING", f"任务回调执行失败: {str(e)}")
            
            # 尝试执行下一个任务
            self._try_execute_next_task()
            
        except Exception as e:
            self.logMessage.emit("ERROR", f"处理任务完成回调失败: {str(e)}")
    
    def _handle_task_failure(self, task: StudyTask):
        """处理任务失败"""
        try:
            if task.retry_count < task.max_retries:
                # 重试任务
                task.retry_count += 1
                task.status = TaskStatus.RETRYING
                task.start_time = None
                
                # 重新加入队列
                self.task_queue.append(task)
                self.task_queue.sort(key=lambda t: t.priority.value, reverse=True)
                
                self.logMessage.emit("WARNING", 
                    f"学习任务失败，准备重试 ({task.retry_count}/{task.max_retries}): {task.user_phone}")
                
                # 延迟后重试 - 使用线程安全的方式
                import threading
                timer = threading.Timer(5.0, self._try_execute_next_task)
                timer.start()
                
            else:
                # 重试次数用完，标记为失败
                task.status = TaskStatus.FAILED
                task.end_time = time.time()
                
                self.stats['failed_tasks'] += 1
                self.taskFailed.emit(task.task_id, task.user_phone, task.error_message)
                self.logMessage.emit("ERROR", 
                    f"学习任务最终失败: {task.user_phone}, 错误: {task.error_message}")
                
        except Exception as e:
            self.logMessage.emit("ERROR", f"处理任务失败逻辑出错: {str(e)}")
    
    def _handle_task_error(self, task: StudyTask, error_msg: str):
        """处理任务错误"""
        task.error_message = error_msg
        task.status = TaskStatus.FAILED
        task.end_time = time.time()
        
        self.stats['failed_tasks'] += 1
        self.taskFailed.emit(task.task_id, task.user_phone, error_msg)
        self.logMessage.emit("ERROR", f"任务错误: {task.user_phone} - {error_msg}")
    
    def _update_pool_status(self):
        """更新线程池状态"""
        try:
            # 计算运行时间
            runtime = time.time() - self.stats['start_time']
            
            # 更新状态
            status = {
                'max_workers': self.max_workers,
                'active_workers': len(self.running_tasks),
                'pending_tasks': len([t for t in self.tasks.values() if t.status == TaskStatus.PENDING]),
                'running_tasks': len([t for t in self.tasks.values() if t.status == TaskStatus.RUNNING]),
                'completed_tasks': self.stats['completed_tasks'],
                'failed_tasks': self.stats['failed_tasks'],
                'total_tasks': self.stats['total_tasks'],
                'runtime': runtime
            }
            
            self.poolStatusChanged.emit(status)
            
        except Exception as e:
            self.logMessage.emit("WARNING", f"更新线程池状态失败: {str(e)}")
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[Dict[str, Any]]: 任务状态信息
        """
        task = self.tasks.get(task_id)
        return task.to_dict() if task else None
    
    def get_all_tasks(self) -> List[Dict[str, Any]]:
        """
        获取所有任务状态
        
        Returns:
            List[Dict[str, Any]]: 所有任务状态列表
        """
        return [task.to_dict() for task in self.tasks.values()]
    
    def cancel_task(self, task_id: str) -> bool:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功取消
        """
        try:
            task = self.tasks.get(task_id)
            if not task:
                return False
            
            if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                return False
            
            # 取消运行中的任务
            if task_id in self.running_tasks:
                future = self.running_tasks[task_id]
                future.cancel()
                del self.running_tasks[task_id]
                self.stats['running_tasks'] -= 1
            
            # 从队列中移除
            if task in self.task_queue:
                self.task_queue.remove(task)
                self.stats['pending_tasks'] -= 1
            
            # 更新任务状态
            task.status = TaskStatus.CANCELLED
            task.end_time = time.time()
            
            self.logMessage.emit("INFO", f"任务已取消: {task.user_phone}")
            return True
            
        except Exception as e:
            self.logMessage.emit("ERROR", f"取消任务失败: {str(e)}")
            return False
    
    def clear_completed_tasks(self):
        """清理已完成的任务"""
        try:
            completed_tasks = [
                task_id for task_id, task in self.tasks.items()
                if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]
            ]
            
            for task_id in completed_tasks:
                del self.tasks[task_id]
            
            self.logMessage.emit("INFO", f"清理了 {len(completed_tasks)} 个已完成的任务")
            
        except Exception as e:
            self.logMessage.emit("ERROR", f"清理已完成任务失败: {str(e)}")
    
    def shutdown(self, wait: bool = True):
        """
        关闭线程池
        
        Args:
            wait: 是否等待所有任务完成
        """
        try:
            self.logMessage.emit("INFO", "正在关闭学习线程池...")
            
            # 停止定时器
            if self.status_timer.isActive():
                self.status_timer.stop()
            
            # 关闭线程池
            self.executor.shutdown(wait=wait)
            
            self.logMessage.emit("INFO", "学习线程池已关闭")
            
        except Exception as e:
            self.logMessage.emit("ERROR", f"关闭线程池失败: {str(e)}")
    
    def get_pool_statistics(self) -> Dict[str, Any]:
        """
        获取线程池统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        runtime = time.time() - self.stats['start_time']
        
        return {
            'max_workers': self.max_workers,
            'total_tasks': self.stats['total_tasks'],
            'completed_tasks': self.stats['completed_tasks'],
            'failed_tasks': self.stats['failed_tasks'],
            'running_tasks': len(self.running_tasks),
            'pending_tasks': len(self.task_queue),
            'success_rate': (self.stats['completed_tasks'] / max(self.stats['total_tasks'], 1)) * 100,
            'runtime': runtime,
            'tasks_per_hour': (self.stats['completed_tasks'] / max(runtime / 3600, 0.01))
        }
