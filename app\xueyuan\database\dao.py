# coding: utf-8
"""
数据访问层模块

该模块提供数据库操作的抽象接口，包括用户、课程、日志和API数据的CRUD操作。

主要功能：
- 用户数据的增删改查
- 课程数据的管理
- 日志记录的存储和查询
- API数据的捕获和处理

类说明：
- UserDAO: 用户数据访问对象
- CourseDAO: 课程数据访问对象
- LogDAO: 日志数据访问对象
- APIDataDAO: API数据访问对象

作者: 小帅工具箱
版本: v1.0
"""

import json
import sqlite3
from datetime import datetime
from typing import Optional, List, Dict, Any
from .models import db_manager, User, Course, LogEntry, APIData


class UserDAO:
    """用户数据访问对象"""
    
    def __init__(self):
        self.db = db_manager
    
    def create_user(self, user: User) -> bool:
        """
        创建用户
        
        Args:
            user: 用户对象
            
        Returns:
            bool: 创建成功返回True，否则返回False
        """
        try:
            with self.db.get_connection() as conn:
                conn.execute('''
                    INSERT INTO users (phone, password, name, status, complete_status,
                                     online_total_credit, compulsory_credit, electives_credit,
                                     last_login_time, error_message, progress)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (user.phone, user.password, user.name, user.status, user.complete_status,
                      user.online_total_credit, user.compulsory_credit, user.electives_credit,
                      user.last_login_time, user.error_message, user.progress))
                conn.commit()
                return True
        except sqlite3.IntegrityError:
            return False  # 用户已存在
        except Exception as e:
            print(f"创建用户失败: {e}")
            return False
    
    def get_user_by_phone(self, phone: str) -> Optional[User]:
        """
        根据手机号获取用户
        
        Args:
            phone: 手机号
            
        Returns:
            User: 用户对象，如果不存在返回None
        """
        try:
            with self.db.get_connection() as conn:
                cursor = conn.execute('SELECT * FROM users WHERE phone = ?', (phone,))
                row = cursor.fetchone()
                if row:
                    return User(
                        phone=row['phone'],
                        password=row['password'],
                        name=row['name'],
                        status=row['status'],
                        complete_status=row['complete_status'],
                        online_total_credit=row['online_total_credit'],
                        compulsory_credit=row['compulsory_credit'],
                        electives_credit=row['electives_credit'],
                        last_login_time=row['last_login_time'],
                        error_message=row['error_message'],
                        progress=row['progress'],
                        created_at=row['created_at'],
                        updated_at=row['updated_at']
                    )
                return None
        except Exception as e:
            print(f"获取用户失败: {e}")
            return None
    
    def get_all_users(self) -> List[User]:
        """
        获取所有用户
        
        Returns:
            List[User]: 用户列表
        """
        users = []
        try:
            with self.db.get_connection() as conn:
                cursor = conn.execute('SELECT * FROM users ORDER BY created_at DESC')
                for row in cursor.fetchall():
                    user = User(
                        phone=row['phone'],
                        password=row['password'],
                        name=row['name'],
                        status=row['status'],
                        complete_status=row['complete_status'],
                        online_total_credit=row['online_total_credit'],
                        compulsory_credit=row['compulsory_credit'],
                        electives_credit=row['electives_credit'],
                        last_login_time=row['last_login_time'],
                        error_message=row['error_message'],
                        progress=row['progress'],
                        created_at=row['created_at'],
                        updated_at=row['updated_at']
                    )
                    users.append(user)
        except Exception as e:
            print(f"获取用户列表失败: {e}")
        return users
    
    def update_user_status(self, phone: str, status: str, error_message: str = "") -> bool:
        """
        更新用户状态
        
        Args:
            phone: 手机号
            status: 新状态
            error_message: 错误信息
            
        Returns:
            bool: 更新成功返回True，否则返回False
        """
        try:
            with self.db.get_connection() as conn:
                conn.execute('''
                    UPDATE users 
                    SET status = ?, error_message = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE phone = ?
                ''', (status, error_message, phone))
                conn.commit()
                return True
        except Exception as e:
            print(f"更新用户状态失败: {e}")
            return False

    def update_user_basic_info(self, phone: str, update_data: Dict[str, Any]) -> bool:
        """
        更新用户基本信息

        Args:
            phone: 手机号
            update_data: 要更新的数据字典

        Returns:
            bool: 更新成功返回True，否则返回False
        """
        try:
            with self.db.get_connection() as conn:
                # 构建更新语句
                update_fields = []
                update_values = []

                for field, value in update_data.items():
                    if field in ['password', 'name', 'status', 'complete_status',
                                'online_total_credit', 'compulsory_credit', 'electives_credit',
                                'last_login_time', 'error_message', 'progress']:
                        update_fields.append(f"{field} = ?")
                        update_values.append(value)

                if not update_fields:
                    return False

                # 添加更新时间
                update_fields.append("updated_at = CURRENT_TIMESTAMP")
                update_values.append(phone)

                sql = f"UPDATE users SET {', '.join(update_fields)} WHERE phone = ?"
                conn.execute(sql, update_values)
                conn.commit()
                return True
        except Exception as e:
            print(f"更新用户基本信息失败: {e}")
            return False

    def update_user_from_api(self, phone: str, api_data: Dict[str, Any]) -> bool:
        """
        从API数据更新用户信息
        
        Args:
            phone: 手机号
            api_data: API返回的数据
            
        Returns:
            bool: 更新成功返回True，否则返回False
        """
        try:
            with self.db.get_connection() as conn:
                # 计算进度
                total_credit = float(api_data.get('onlineTotalCredit', 0))
                compulsory_credit = float(api_data.get('compulsoryCredit', 0))
                electives_credit = float(api_data.get('electivesCredit', 0))
                progress = 0.0
                if total_credit > 0:
                    progress = ((compulsory_credit + electives_credit) / total_credit) * 100
                
                conn.execute('''
                    UPDATE users 
                    SET name = ?, complete_status = ?, online_total_credit = ?,
                        compulsory_credit = ?, electives_credit = ?, progress = ?,
                        last_login_time = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
                    WHERE phone = ?
                ''', (api_data.get('name', ''), api_data.get('completeStatus', '1'),
                      total_credit, compulsory_credit, electives_credit, progress, phone))
                conn.commit()
                return True
        except Exception as e:
            print(f"从API数据更新用户信息失败: {e}")
            return False
    
    def delete_user(self, phone: str) -> bool:
        """
        删除用户
        
        Args:
            phone: 手机号
            
        Returns:
            bool: 删除成功返回True，否则返回False
        """
        try:
            with self.db.get_connection() as conn:
                # 先删除相关的课程数据
                conn.execute('DELETE FROM courses WHERE user_phone = ?', (phone,))
                # 删除相关的API数据
                conn.execute('DELETE FROM api_data WHERE user_phone = ?', (phone,))
                # 删除用户
                conn.execute('DELETE FROM users WHERE phone = ?', (phone,))
                conn.commit()
                return True
        except Exception as e:
            print(f"删除用户失败: {e}")
            return False
    
    def batch_import_users(self, users_data: List[Dict[str, str]]) -> int:
        """
        批量导入用户
        
        Args:
            users_data: 用户数据列表，每个元素包含phone、password、name等字段
            
        Returns:
            int: 成功导入的用户数量
        """
        success_count = 0
        for user_data in users_data:
            user = User(
                phone=user_data.get('phone', ''),
                password=user_data.get('password', ''),
                name=user_data.get('name', '')
            )
            if self.create_user(user):
                success_count += 1
        return success_count


class CourseDAO:
    """课程数据访问对象"""
    
    def __init__(self):
        self.db = db_manager
    
    def create_course(self, course: Course) -> bool:
        """
        创建课程记录
        
        Args:
            course: 课程对象
            
        Returns:
            bool: 创建成功返回True，否则返回False
        """
        try:
            with self.db.get_connection() as conn:
                conn.execute('''
                    INSERT INTO courses (user_phone, course_type, name, course_id, completed,
                                       credit, percentage, courseware_id, video_url, completed_date)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (course.user_phone, course.course_type, course.name, course.course_id,
                      course.completed, course.credit, course.percentage, course.courseware_id,
                      course.video_url, course.completed_date))
                conn.commit()
                return True
        except Exception as e:
            print(f"创建课程记录失败: {e}")
            return False
    
    def get_courses_by_user(self, phone: str, course_type: str = None) -> List[Course]:
        """
        获取用户的课程列表
        
        Args:
            phone: 用户手机号
            course_type: 课程类型，可选
            
        Returns:
            List[Course]: 课程列表
        """
        courses = []
        try:
            with self.db.get_connection() as conn:
                if course_type:
                    cursor = conn.execute('''
                        SELECT * FROM courses 
                        WHERE user_phone = ? AND course_type = ?
                        ORDER BY created_at DESC
                    ''', (phone, course_type))
                else:
                    cursor = conn.execute('''
                        SELECT * FROM courses 
                        WHERE user_phone = ?
                        ORDER BY created_at DESC
                    ''', (phone,))
                
                for row in cursor.fetchall():
                    course = Course(
                        user_phone=row['user_phone'],
                        course_type=row['course_type'],
                        name=row['name'],
                        course_id=row['course_id'],
                        completed=row['completed'],
                        credit=row['credit'],
                        percentage=row['percentage'],
                        courseware_id=row['courseware_id'],
                        video_url=row['video_url'],
                        completed_date=row['completed_date'],
                        created_at=row['created_at'],
                        updated_at=row['updated_at']
                    )
                    courses.append(course)
        except Exception as e:
            print(f"获取用户课程列表失败: {e}")
        return courses

    def update_course_progress(self, user_phone: str, course_id: str,
                              percentage: float, completed: bool = False) -> bool:
        """
        更新课程学习进度

        Args:
            user_phone: 用户手机号
            course_id: 课程ID
            percentage: 完成百分比
            completed: 是否已完成

        Returns:
            bool: 更新成功返回True，否则返回False
        """
        try:
            with self.db.get_connection() as conn:
                completed_date = datetime.now().isoformat() if completed else None
                conn.execute('''
                    UPDATE courses
                    SET percentage = ?, completed = ?, completed_date = ?, updated_at = ?
                    WHERE user_phone = ? AND course_id = ?
                ''', (percentage, completed, completed_date, datetime.now().isoformat(),
                      user_phone, course_id))
                conn.commit()
                return True
        except Exception as e:
            print(f"更新课程进度失败: {e}")
            return False

    def get_incomplete_courses(self, user_phone: str) -> List[Course]:
        """
        获取用户未完成的课程

        Args:
            user_phone: 用户手机号

        Returns:
            List[Course]: 未完成的课程列表
        """
        courses = []
        try:
            with self.db.get_connection() as conn:
                cursor = conn.execute('''
                    SELECT * FROM courses
                    WHERE user_phone = ? AND completed = 0
                    ORDER BY course_type, created_at
                ''', (user_phone,))

                for row in cursor.fetchall():
                    course = Course(
                        user_phone=row['user_phone'],
                        course_type=row['course_type'],
                        name=row['name'],
                        course_id=row['course_id'],
                        completed=row['completed'],
                        credit=row['credit'],
                        percentage=row['percentage'],
                        courseware_id=row['courseware_id'],
                        video_url=row['video_url'],
                        completed_date=row['completed_date'],
                        created_at=row['created_at'],
                        updated_at=row['updated_at']
                    )
                    courses.append(course)
        except Exception as e:
            print(f"获取未完成课程失败: {e}")
        return courses

    def batch_create_courses(self, courses: List[Course]) -> int:
        """
        批量创建课程记录

        Args:
            courses: 课程对象列表

        Returns:
            int: 成功创建的课程数量
        """
        success_count = 0
        try:
            with self.db.get_connection() as conn:
                for course in courses:
                    try:
                        conn.execute('''
                            INSERT OR REPLACE INTO courses
                            (user_phone, course_type, name, course_id, completed,
                             credit, percentage, courseware_id, video_url, completed_date)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (course.user_phone, course.course_type, course.name, course.course_id,
                              course.completed, course.credit, course.percentage, course.courseware_id,
                              course.video_url, course.completed_date))
                        success_count += 1
                    except Exception as e:
                        print(f"创建课程记录失败 {course.name}: {e}")
                conn.commit()
        except Exception as e:
            print(f"批量创建课程记录失败: {e}")
        return success_count

    def delete_user_courses(self, user_phone: str) -> bool:
        """
        删除用户的所有课程记录

        Args:
            user_phone: 用户手机号

        Returns:
            bool: 删除成功返回True，否则返回False
        """
        try:
            with self.db.get_connection() as conn:
                conn.execute('DELETE FROM courses WHERE user_phone = ?', (user_phone,))
                conn.commit()
                return True
        except Exception as e:
            print(f"删除用户课程记录失败: {e}")
            return False

    def get_course_statistics(self, user_phone: str) -> Dict[str, Any]:
        """
        获取用户课程统计信息

        Args:
            user_phone: 用户手机号

        Returns:
            Dict[str, Any]: 统计信息
        """
        stats = {
            'total_courses': 0,
            'completed_courses': 0,
            'compulsory_courses': 0,
            'elective_courses': 0,
            'total_credit': 0.0,
            'completed_credit': 0.0,
            'completion_rate': 0.0
        }

        try:
            with self.db.get_connection() as conn:
                # 总课程数和完成数
                cursor = conn.execute('''
                    SELECT
                        COUNT(*) as total,
                        SUM(CASE WHEN completed = 1 THEN 1 ELSE 0 END) as completed,
                        SUM(CASE WHEN course_type = '必修课' THEN 1 ELSE 0 END) as compulsory,
                        SUM(CASE WHEN course_type = '选修课' THEN 1 ELSE 0 END) as elective,
                        SUM(credit) as total_credit,
                        SUM(CASE WHEN completed = 1 THEN credit ELSE 0 END) as completed_credit
                    FROM courses WHERE user_phone = ?
                ''', (user_phone,))

                row = cursor.fetchone()
                if row:
                    stats['total_courses'] = row['total'] or 0
                    stats['completed_courses'] = row['completed'] or 0
                    stats['compulsory_courses'] = row['compulsory'] or 0
                    stats['elective_courses'] = row['elective'] or 0
                    stats['total_credit'] = row['total_credit'] or 0.0
                    stats['completed_credit'] = row['completed_credit'] or 0.0

                    if stats['total_courses'] > 0:
                        stats['completion_rate'] = (stats['completed_courses'] / stats['total_courses']) * 100

        except Exception as e:
            print(f"获取课程统计信息失败: {e}")

        return stats

    def update_course_completion(self, user_phone: str, course_id: str, completed: str = "1",
                               completed_date: str = None) -> bool:
        """
        更新课程完成状态

        Args:
            user_phone: 用户手机号
            course_id: 课程ID
            completed: 完成状态
            completed_date: 完成日期

        Returns:
            bool: 更新成功返回True，否则返回False
        """
        try:
            if completed_date is None:
                completed_date = datetime.now().strftime('%Y-%m-%d')

            with self.db.get_connection() as conn:
                conn.execute('''
                    UPDATE courses
                    SET completed = ?, completed_date = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE user_phone = ? AND course_id = ?
                ''', (completed, completed_date, user_phone, course_id))
                conn.commit()
                return True
        except Exception as e:
            print(f"更新课程完成状态失败: {e}")
            return False

    def batch_save_courses(self, courses: List[Course]) -> int:
        """
        批量保存课程数据

        Args:
            courses: 课程列表

        Returns:
            int: 成功保存的课程数量
        """
        success_count = 0
        for course in courses:
            # 先检查课程是否已存在
            existing = self.get_course_by_id(course.user_phone, course.course_id)
            if existing:
                # 更新现有课程
                if self.update_course(course):
                    success_count += 1
            else:
                # 创建新课程
                if self.create_course(course):
                    success_count += 1
        return success_count

    def get_course_by_id(self, user_phone: str, course_id: str) -> Optional[Course]:
        """
        根据课程ID获取课程

        Args:
            user_phone: 用户手机号
            course_id: 课程ID

        Returns:
            Course: 课程对象，如果不存在返回None
        """
        try:
            with self.db.get_connection() as conn:
                cursor = conn.execute('''
                    SELECT * FROM courses
                    WHERE user_phone = ? AND course_id = ?
                ''', (user_phone, course_id))
                row = cursor.fetchone()
                if row:
                    return Course(
                        user_phone=row['user_phone'],
                        course_type=row['course_type'],
                        name=row['name'],
                        course_id=row['course_id'],
                        completed=row['completed'],
                        credit=row['credit'],
                        percentage=row['percentage'],
                        courseware_id=row['courseware_id'],
                        video_url=row['video_url'],
                        completed_date=row['completed_date'],
                        created_at=row['created_at'],
                        updated_at=row['updated_at']
                    )
                return None
        except Exception as e:
            print(f"获取课程失败: {e}")
            return None

    def update_course(self, course: Course) -> bool:
        """
        更新课程信息

        Args:
            course: 课程对象

        Returns:
            bool: 更新成功返回True，否则返回False
        """
        try:
            with self.db.get_connection() as conn:
                conn.execute('''
                    UPDATE courses
                    SET name = ?, completed = ?, credit = ?, percentage = ?,
                        courseware_id = ?, video_url = ?, completed_date = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE user_phone = ? AND course_id = ?
                ''', (course.name, course.completed, course.credit, course.percentage,
                      course.courseware_id, course.video_url, course.completed_date,
                      course.user_phone, course.course_id))
                conn.commit()
                return True
        except Exception as e:
            print(f"更新课程信息失败: {e}")
            return False


class LogDAO:
    """日志数据访问对象"""

    def __init__(self):
        self.db = db_manager

    def create_log(self, log_entry: LogEntry) -> bool:
        """
        创建日志记录

        Args:
            log_entry: 日志条目对象

        Returns:
            bool: 创建成功返回True，否则返回False
        """
        try:
            with self.db.get_connection() as conn:
                conn.execute('''
                    INSERT INTO logs (level, message, module, user_phone, details)
                    VALUES (?, ?, ?, ?, ?)
                ''', (log_entry.level, log_entry.message, log_entry.module, log_entry.user_phone, log_entry.details))
                conn.commit()
                return True
        except Exception as e:
            print(f"创建日志记录失败: {e}")
            return False

    def get_logs(self, level: str = None, module: str = None, user_phone: str = None,
                 limit: int = 1000) -> List[LogEntry]:
        """
        获取日志记录

        Args:
            level: 日志级别过滤
            module: 模块过滤
            user_phone: 用户过滤
            limit: 返回记录数限制

        Returns:
            List[LogEntry]: 日志记录列表
        """
        logs = []
        try:
            with self.db.get_connection() as conn:
                query = 'SELECT * FROM logs WHERE 1=1'
                params = []

                if level:
                    query += ' AND level = ?'
                    params.append(level)
                if module:
                    query += ' AND module = ?'
                    params.append(module)
                if user_phone:
                    query += ' AND user_phone = ?'
                    params.append(user_phone)

                query += ' ORDER BY created_at DESC LIMIT ?'
                params.append(limit)

                cursor = conn.execute(query, params)
                for row in cursor.fetchall():
                    log_entry = LogEntry(
                        level=row['level'],
                        message=row['message'],
                        module=row['module'],
                        user_phone=row['user_phone'],
                        created_at=row['created_at']
                    )
                    logs.append(log_entry)
        except Exception as e:
            print(f"获取日志记录失败: {e}")
        return logs

    def clear_old_logs(self, days: int = 30) -> int:
        """
        清理旧日志

        Args:
            days: 保留天数

        Returns:
            int: 删除的记录数
        """
        try:
            with self.db.get_connection() as conn:
                cursor = conn.execute('''
                    DELETE FROM logs
                    WHERE created_at < datetime('now', '-{} days')
                '''.format(days))
                conn.commit()
                return cursor.rowcount
        except Exception as e:
            print(f"清理旧日志失败: {e}")
            return 0

    def get_all_logs(self, limit: int = 5000) -> List[LogEntry]:
        """
        获取所有日志记录

        Args:
            limit: 返回记录数限制

        Returns:
            List[LogEntry]: 日志记录列表
        """
        logs = []
        try:
            with self.db.get_connection() as conn:
                cursor = conn.execute('''
                    SELECT id, level, message, module, user_phone, created_at, details
                    FROM logs
                    ORDER BY created_at DESC
                    LIMIT ?
                ''', (limit,))

                for row in cursor.fetchall():
                    log_entry = LogEntry(
                        level=row['level'],
                        message=row['message'],
                        module=row['module'],
                        user_phone=row['user_phone'],
                        details=row['details'] if 'details' in row.keys() else "",
                        created_at=row['created_at']
                    )
                    log_entry.id = row['id']
                    logs.append(log_entry)

        except Exception as e:
            print(f"获取所有日志失败: {e}")
        return logs

    def clear_all_logs(self) -> bool:
        """
        清空所有日志记录

        Returns:
            bool: 清空成功返回True，否则返回False
        """
        try:
            with self.db.get_connection() as conn:
                conn.execute('DELETE FROM logs')
                conn.commit()
                return True
        except Exception as e:
            print(f"清空所有日志失败: {e}")
            return False


class APIDataDAO:
    """API数据访问对象"""

    def __init__(self):
        self.db = db_manager

    def create_api_data(self, api_data: APIData) -> bool:
        """
        创建API数据记录

        Args:
            api_data: API数据对象

        Returns:
            bool: 创建成功返回True，否则返回False
        """
        try:
            with self.db.get_connection() as conn:
                conn.execute('''
                    INSERT INTO api_data (user_phone, api_url, request_method, response_status,
                                        response_data, is_processed, error_message)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (api_data.user_phone, api_data.api_url, api_data.request_method,
                      api_data.response_status, api_data.response_data, api_data.is_processed,
                      api_data.error_message))
                conn.commit()
                return True
        except Exception as e:
            print(f"创建API数据记录失败: {e}")
            return False

    def get_api_data_by_url(self, user_phone: str, api_url: str) -> List[APIData]:
        """
        根据API URL获取数据

        Args:
            user_phone: 用户手机号
            api_url: API地址

        Returns:
            List[APIData]: API数据列表
        """
        api_data_list = []
        try:
            with self.db.get_connection() as conn:
                cursor = conn.execute('''
                    SELECT * FROM api_data
                    WHERE user_phone = ? AND api_url = ?
                    ORDER BY capture_time DESC
                ''', (user_phone, api_url))

                for row in cursor.fetchall():
                    api_data = APIData(
                        user_phone=row['user_phone'],
                        api_url=row['api_url'],
                        request_method=row['request_method'],
                        response_status=row['response_status'],
                        response_data=row['response_data'],
                        capture_time=row['capture_time'],
                        is_processed=row['is_processed'],
                        error_message=row['error_message']
                    )
                    api_data_list.append(api_data)
        except Exception as e:
            print(f"获取API数据失败: {e}")
        return api_data_list

    def mark_as_processed(self, user_phone: str, api_url: str) -> bool:
        """
        标记API数据为已处理

        Args:
            user_phone: 用户手机号
            api_url: API地址

        Returns:
            bool: 更新成功返回True，否则返回False
        """
        try:
            with self.db.get_connection() as conn:
                conn.execute('''
                    UPDATE api_data
                    SET is_processed = TRUE
                    WHERE user_phone = ? AND api_url = ?
                ''', (user_phone, api_url))
                conn.commit()
                return True
        except Exception as e:
            print(f"标记API数据为已处理失败: {e}")
            return False


# 创建DAO实例
user_dao = UserDAO()
course_dao = CourseDAO()
log_dao = LogDAO()
api_data_dao = APIDataDAO()
