# coding: utf-8
"""
数据库模型定义模块

该模块定义了学习工具的数据库表结构和模型类，包括：
- 用户数据表 (users)
- 课程数据表 (courses)
- 日志数据表 (logs)
- API数据表 (api_data)

主要功能：
- 定义数据库表结构
- 提供数据模型类
- 支持数据库初始化和迁移

作者: 小帅工具箱
版本: v1.0
"""

import sqlite3
import os
from datetime import datetime
from typing import Optional, List, Dict, Any
from ..constants import UserStatus, CompleteStatus


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = "data/database/study.db"):
        """
        初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.ensure_database_directory()
        self.init_database()
    
    def ensure_database_directory(self):
        """确保数据库目录存在"""
        db_dir = os.path.dirname(self.db_path)
        if not os.path.exists(db_dir):
            os.makedirs(db_dir, exist_ok=True)
    
    def get_connection(self) -> sqlite3.Connection:
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使查询结果可以通过列名访问
        return conn
    
    def init_database(self):
        """初始化数据库表结构"""
        with self.get_connection() as conn:
            # 创建用户数据表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    phone TEXT UNIQUE NOT NULL,           -- 手机号码
                    password TEXT NOT NULL,               -- 密码
                    name TEXT,                           -- 姓名
                    status TEXT DEFAULT '未开始',         -- 状态
                    complete_status TEXT DEFAULT '1',     -- 完成状态
                    online_total_credit REAL DEFAULT 0,  -- 总学时
                    compulsory_credit REAL DEFAULT 0,    -- 必修学时
                    electives_credit REAL DEFAULT 0,     -- 选修学时
                    last_login_time DATETIME,            -- 最后登录时间
                    error_message TEXT,                  -- 错误信息
                    progress REAL DEFAULT 0,             -- 进度
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建课程数据表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS courses (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_phone TEXT NOT NULL,            -- 用户手机号
                    course_type TEXT NOT NULL,           -- 课程类型(必修课/选修课)
                    name TEXT NOT NULL,                  -- 课程名称
                    course_id TEXT NOT NULL,             -- 课程ID
                    completed TEXT DEFAULT '0',          -- 完成状态
                    credit INTEGER DEFAULT 0,            -- 学时
                    percentage TEXT DEFAULT '0',         -- 完成进度
                    courseware_id TEXT,                  -- 课件ID
                    video_url TEXT,                      -- 视频URL
                    completed_date DATE,                 -- 完成日期
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_phone) REFERENCES users(phone)
                )
            ''')
            
            # 创建日志数据表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    level TEXT NOT NULL,                 -- 日志级别
                    message TEXT NOT NULL,               -- 日志消息
                    module TEXT,                         -- 模块名称
                    user_phone TEXT,                     -- 相关用户
                    details TEXT,                        -- 详细信息
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建API数据表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS api_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_phone TEXT NOT NULL,            -- 用户手机号
                    api_url TEXT NOT NULL,               -- API地址
                    request_method TEXT DEFAULT 'GET',   -- 请求方法
                    response_status INTEGER,             -- 响应状态码
                    response_data TEXT,                  -- 响应数据(JSON格式)
                    capture_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                    is_processed BOOLEAN DEFAULT FALSE,  -- 是否已处理
                    error_message TEXT,                  -- 错误信息
                    FOREIGN KEY (user_phone) REFERENCES users(phone)
                )
            ''')
            
            # 创建索引以提高查询性能
            conn.execute('CREATE INDEX IF NOT EXISTS idx_users_phone ON users(phone)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_courses_user_phone ON courses(user_phone)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_courses_type ON courses(course_type)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_logs_level ON logs(level)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_logs_created_at ON logs(created_at)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_api_data_user_phone ON api_data(user_phone)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_api_data_api_url ON api_data(api_url)')
            
            conn.commit()


class User:
    """用户数据模型"""
    
    def __init__(self, phone: str, password: str, name: str = "", **kwargs):
        self.phone = phone
        self.password = password
        self.name = name
        self.status = kwargs.get('status', UserStatus.NOT_STARTED.value)
        self.complete_status = kwargs.get('complete_status', CompleteStatus.NOT_COMPLETED.value)
        self.online_total_credit = kwargs.get('online_total_credit', 0.0)
        self.compulsory_credit = kwargs.get('compulsory_credit', 0.0)
        self.electives_credit = kwargs.get('electives_credit', 0.0)
        self.last_login_time = kwargs.get('last_login_time')
        self.error_message = kwargs.get('error_message', '')
        self.progress = kwargs.get('progress', 0.0)
        self.created_at = kwargs.get('created_at')
        self.updated_at = kwargs.get('updated_at')
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'phone': self.phone,
            'password': self.password,
            'name': self.name,
            'status': self.status,
            'complete_status': self.complete_status,
            'online_total_credit': self.online_total_credit,
            'compulsory_credit': self.compulsory_credit,
            'electives_credit': self.electives_credit,
            'last_login_time': self.last_login_time,
            'error_message': self.error_message,
            'progress': self.progress,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }


class Course:
    """课程数据模型"""
    
    def __init__(self, user_phone: str, course_type: str, name: str, course_id: str, **kwargs):
        self.user_phone = user_phone
        self.course_type = course_type
        self.name = name
        self.course_id = course_id
        self.completed = kwargs.get('completed', '0')
        self.credit = kwargs.get('credit', 0)
        self.percentage = kwargs.get('percentage', '0')
        self.courseware_id = kwargs.get('courseware_id', '')
        self.video_url = kwargs.get('video_url', '')
        self.completed_date = kwargs.get('completed_date')
        self.created_at = kwargs.get('created_at')
        self.updated_at = kwargs.get('updated_at')
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'user_phone': self.user_phone,
            'course_type': self.course_type,
            'name': self.name,
            'course_id': self.course_id,
            'completed': self.completed,
            'credit': self.credit,
            'percentage': self.percentage,
            'courseware_id': self.courseware_id,
            'video_url': self.video_url,
            'completed_date': self.completed_date,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }


class LogEntry:
    """日志条目数据模型"""

    def __init__(self, level: str, message: str, module: str = "", user_phone: str = "", details: str = "", **kwargs):
        self.level = level
        self.message = message
        self.module = module
        self.user_phone = user_phone
        self.details = details
        self.created_at = kwargs.get('created_at')

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'level': self.level,
            'message': self.message,
            'module': self.module,
            'user_phone': self.user_phone,
            'details': self.details,
            'created_at': self.created_at
        }


class APIData:
    """API数据模型"""
    
    def __init__(self, user_phone: str, api_url: str, **kwargs):
        self.user_phone = user_phone
        self.api_url = api_url
        self.request_method = kwargs.get('request_method', 'GET')
        self.response_status = kwargs.get('response_status')
        self.response_data = kwargs.get('response_data', '')
        self.capture_time = kwargs.get('capture_time')
        self.is_processed = kwargs.get('is_processed', False)
        self.error_message = kwargs.get('error_message', '')
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'user_phone': self.user_phone,
            'api_url': self.api_url,
            'request_method': self.request_method,
            'response_status': self.response_status,
            'response_data': self.response_data,
            'capture_time': self.capture_time,
            'is_processed': self.is_processed,
            'error_message': self.error_message
        }


# 全局数据库管理器实例
db_manager = DatabaseManager()
