# coding: utf-8
"""
批量导入对话框模块

该模块定义了用户批量导入对话框，支持从文件导入用户数据。

主要功能：
- 支持Excel和CSV文件导入
- 数据预览和验证
- 导入进度显示
- 错误处理和报告

类说明：
- ImportDialog: 批量导入对话框

作者: 小帅工具箱
版本: v1.0
"""

import os
import csv
import re
from typing import List, Dict, Any
from PySide6.QtCore import Qt, Signal, QThread, QTimer
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QFileDialog, QTextEdit, QProgressBar)
from qfluentwidgets import (PrimaryPushButton, PushButton, BodyLabel, SubtitleLabel,
                            TableWidget, InfoBar, InfoBarPosition, ProgressRing,
                            FluentIcon as FIF)

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

from ...database.models import User
from ...database.dao import user_dao


class ImportWorker(QThread):
    """导入工作线程"""
    
    progressChanged = Signal(int, int)  # 当前进度, 总数
    userImported = Signal(str, bool, str)  # 手机号, 成功状态, 消息
    finished = Signal(int, int, int)  # 成功数, 失败数, 跳过数
    
    def __init__(self, users_data: List[Dict[str, str]]):
        super().__init__()
        self.users_data = users_data
        self.should_stop = False
    
    def run(self):
        """执行导入"""
        success_count = 0
        failed_count = 0
        skipped_count = 0
        total = len(self.users_data)
        
        for i, user_data in enumerate(self.users_data):
            if self.should_stop:
                break
            
            try:
                phone = user_data.get('phone', '').strip()
                password = user_data.get('password', '').strip()
                name = user_data.get('name', '').strip()
                
                # 验证数据
                if not phone or not password:
                    self.userImported.emit(phone or f"第{i+1}行", False, "手机号或密码为空")
                    failed_count += 1
                    continue
                
                # 检查是否已存在
                existing_user = user_dao.get_user_by_phone(phone)
                if existing_user:
                    self.userImported.emit(phone, False, "用户已存在")
                    skipped_count += 1
                    continue
                
                # 创建用户
                user = User(phone=phone, password=password, name=name)
                success = user_dao.create_user(user)
                
                if success:
                    self.userImported.emit(phone, True, "导入成功")
                    success_count += 1
                else:
                    self.userImported.emit(phone, False, "创建失败")
                    failed_count += 1
                    
            except Exception as e:
                self.userImported.emit(phone or f"第{i+1}行", False, f"异常: {str(e)}")
                failed_count += 1
            
            self.progressChanged.emit(i + 1, total)
        
        self.finished.emit(success_count, failed_count, skipped_count)
    
    def stop(self):
        """停止导入"""
        self.should_stop = True


class ImportDialog(QDialog):
    """
    批量导入对话框
    
    支持从Excel和CSV文件批量导入用户数据。
    """
    
    # 信号定义
    importCompleted = Signal(int)  # 导入完成，传递成功导入的用户数
    
    def __init__(self, parent=None):
        """初始化导入对话框"""
        super().__init__(parent)
        self.setWindowTitle("批量导入用户")
        self.setFixedSize(800, 600)
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)
        
        self.file_path = ""
        self.users_data = []
        self.import_worker = None
        
        self.initUI()
        self.connectSignalToSlot()

    def getMainInterface(self):
        """获取主界面引用"""
        # 向上查找主界面，对话框需要特殊处理
        parent = self.parent()
        while parent:
            if parent.__class__.__name__ == 'StudyMainInterface':
                return parent
            elif hasattr(parent, 'parent') and parent.parent():
                parent = parent.parent()
            else:
                break
        return self

    def initUI(self):
        """初始化用户界面"""
        self.vBoxLayout = QVBoxLayout(self)
        self.vBoxLayout.setContentsMargins(30, 30, 30, 30)
        self.vBoxLayout.setSpacing(20)
        
        # 标题
        self.titleLabel = SubtitleLabel("批量导入用户", self)
        self.vBoxLayout.addWidget(self.titleLabel)
        
        # 文件选择区域
        self.createFileSelectionArea()
        
        # 数据预览区域
        self.createPreviewArea()
        
        # 导入进度区域
        self.createProgressArea()
        
        # 按钮区域
        self.createButtonArea()
    
    def createFileSelectionArea(self):
        """创建文件选择区域"""
        self.fileLayout = QHBoxLayout()
        
        self.fileLabel = BodyLabel("选择文件:", self)
        self.filePathLabel = BodyLabel("未选择文件", self)
        self.filePathLabel.setStyleSheet("color: #999999;")
        
        self.selectFileBtn = PushButton("选择文件", self)
        self.selectFileBtn.setIcon(FIF.FOLDER)
        
        self.fileLayout.addWidget(self.fileLabel)
        self.fileLayout.addWidget(self.filePathLabel, 1)
        self.fileLayout.addWidget(self.selectFileBtn)
        
        self.vBoxLayout.addLayout(self.fileLayout)
        
        # 格式说明
        self.formatLabel = BodyLabel(
            "支持格式: Excel (.xlsx, .xls) 和 CSV (.csv)\n"
            "文件应包含列: phone(手机号), password(密码), name(姓名，可选)",
            self
        )
        self.formatLabel.setStyleSheet("color: #666666; font-size: 12px;")
        self.vBoxLayout.addWidget(self.formatLabel)
    
    def createPreviewArea(self):
        """创建数据预览区域"""
        self.previewLabel = SubtitleLabel("数据预览", self)
        self.vBoxLayout.addWidget(self.previewLabel)
        
        self.previewTable = TableWidget(self)
        self.previewTable.setColumnCount(4)
        self.previewTable.setHorizontalHeaderLabels(["行号", "手机号", "密码", "姓名"])
        self.previewTable.setMaximumHeight(200)
        self.vBoxLayout.addWidget(self.previewTable)
        
        self.previewInfoLabel = BodyLabel("", self)
        self.vBoxLayout.addWidget(self.previewInfoLabel)
    
    def createProgressArea(self):
        """创建导入进度区域"""
        self.progressLabel = SubtitleLabel("导入进度", self)
        self.progressLabel.setVisible(False)
        self.vBoxLayout.addWidget(self.progressLabel)
        
        # 进度条和统计
        self.progressLayout = QHBoxLayout()
        
        self.progressBar = QProgressBar(self)
        self.progressBar.setVisible(False)
        
        self.progressStatsLabel = BodyLabel("", self)
        self.progressStatsLabel.setVisible(False)
        
        self.progressLayout.addWidget(self.progressBar, 2)
        self.progressLayout.addWidget(self.progressStatsLabel, 1)
        
        self.vBoxLayout.addLayout(self.progressLayout)
        
        # 导入日志
        self.logTextEdit = QTextEdit(self)
        self.logTextEdit.setMaximumHeight(150)
        self.logTextEdit.setVisible(False)
        self.vBoxLayout.addWidget(self.logTextEdit)
    
    def createButtonArea(self):
        """创建按钮区域"""
        self.buttonLayout = QHBoxLayout()
        
        self.cancelBtn = PushButton("取消", self)
        self.importBtn = PrimaryPushButton("开始导入", self)
        self.importBtn.setIcon(FIF.DOWNLOAD)
        self.importBtn.setEnabled(False)
        
        self.buttonLayout.addStretch()
        self.buttonLayout.addWidget(self.cancelBtn)
        self.buttonLayout.addWidget(self.importBtn)
        
        self.vBoxLayout.addLayout(self.buttonLayout)
    
    def connectSignalToSlot(self):
        """连接信号到槽函数"""
        self.selectFileBtn.clicked.connect(self.onSelectFile)
        self.cancelBtn.clicked.connect(self.reject)
        self.importBtn.clicked.connect(self.onStartImport)
    
    def onSelectFile(self):
        """选择文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择用户数据文件",
            "",
            "Excel文件 (*.xlsx *.xls);;CSV文件 (*.csv);;所有文件 (*)"
        )
        
        if file_path:
            self.file_path = file_path
            self.filePathLabel.setText(os.path.basename(file_path))
            self.filePathLabel.setStyleSheet("color: #000000;")
            
            # 解析文件
            self.parseFile()
    
    def parseFile(self):
        """解析文件"""
        try:
            if self.file_path.endswith('.csv'):
                self.parseCsvFile()
            elif self.file_path.endswith(('.xlsx', '.xls')):
                self.parseExcelFile()
            else:
                raise Exception("不支持的文件格式")
            
            self.updatePreview()
            self.importBtn.setEnabled(len(self.users_data) > 0)
            
        except Exception as e:
            InfoBar.error(
                title="文件解析失败",
                content=f"解析文件失败: {str(e)}",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=3000,
                parent=self.getMainInterface()
            )
    
    def parseCsvFile(self):
        """解析CSV文件"""
        self.users_data = []
        
        with open(self.file_path, 'r', encoding='utf-8-sig') as file:
            # 尝试检测分隔符
            sample = file.read(1024)
            file.seek(0)
            
            sniffer = csv.Sniffer()
            delimiter = sniffer.sniff(sample).delimiter
            
            reader = csv.DictReader(file, delimiter=delimiter)
            
            for row in reader:
                # 处理不同的列名格式
                phone = self.getColumnValue(row, ['phone', '手机号', '电话', 'mobile'])
                password = self.getColumnValue(row, ['password', '密码', 'pwd'])
                name = self.getColumnValue(row, ['name', '姓名', '用户名', 'username'])
                
                if phone:  # 至少要有手机号
                    self.users_data.append({
                        'phone': phone,
                        'password': password,
                        'name': name
                    })
    
    def parseExcelFile(self):
        """解析Excel文件"""
        if not PANDAS_AVAILABLE:
            raise Exception("需要安装pandas库来支持Excel文件")
        
        self.users_data = []
        
        # 读取Excel文件
        df = pd.read_excel(self.file_path)
        
        for _, row in df.iterrows():
            # 处理不同的列名格式
            phone = self.getColumnValueFromSeries(row, ['phone', '手机号', '电话', 'mobile'])
            password = self.getColumnValueFromSeries(row, ['password', '密码', 'pwd'])
            name = self.getColumnValueFromSeries(row, ['name', '姓名', '用户名', 'username'])
            
            if phone:  # 至少要有手机号
                self.users_data.append({
                    'phone': str(phone).strip(),
                    'password': str(password).strip() if password else '',
                    'name': str(name).strip() if name else ''
                })
    
    def getColumnValue(self, row: dict, possible_names: List[str]) -> str:
        """从行数据中获取列值"""
        for name in possible_names:
            if name in row and row[name]:
                return str(row[name]).strip()
        return ""
    
    def getColumnValueFromSeries(self, row, possible_names: List[str]) -> str:
        """从pandas Series中获取列值"""
        for name in possible_names:
            if name in row.index and pd.notna(row[name]):
                return str(row[name]).strip()
        return ""
    
    def updatePreview(self):
        """更新数据预览"""
        self.previewTable.setRowCount(min(10, len(self.users_data)))
        
        for i, user_data in enumerate(self.users_data[:10]):
            self.previewTable.setItem(i, 0, QTableWidgetItem(str(i + 1)))
            self.previewTable.setItem(i, 1, QTableWidgetItem(user_data['phone']))
            self.previewTable.setItem(i, 2, QTableWidgetItem('*' * len(user_data['password'])))
            self.previewTable.setItem(i, 3, QTableWidgetItem(user_data['name']))
        
        # 更新信息
        total_count = len(self.users_data)
        valid_count = len([u for u in self.users_data if u['phone'] and u['password']])
        
        self.previewInfoLabel.setText(
            f"共解析到 {total_count} 条记录，其中 {valid_count} 条有效记录"
        )
    
    def onStartImport(self):
        """开始导入"""
        if not self.users_data:
            return
        
        # 显示进度界面
        self.progressLabel.setVisible(True)
        self.progressBar.setVisible(True)
        self.progressStatsLabel.setVisible(True)
        self.logTextEdit.setVisible(True)
        
        self.progressBar.setMaximum(len(self.users_data))
        self.progressBar.setValue(0)
        
        # 禁用按钮
        self.importBtn.setEnabled(False)
        self.selectFileBtn.setEnabled(False)
        
        # 启动导入线程
        self.import_worker = ImportWorker(self.users_data)
        self.import_worker.progressChanged.connect(self.onProgressChanged)
        self.import_worker.userImported.connect(self.onUserImported)
        self.import_worker.finished.connect(self.onImportFinished)
        self.import_worker.start()
    
    def onProgressChanged(self, current: int, total: int):
        """进度改变"""
        self.progressBar.setValue(current)
        self.progressStatsLabel.setText(f"{current}/{total}")
    
    def onUserImported(self, phone: str, success: bool, message: str):
        """用户导入结果"""
        status = "成功" if success else "失败"
        color = "green" if success else "red"
        
        log_text = f"<span style='color: {color};'>[{status}] {phone}: {message}</span>"
        self.logTextEdit.append(log_text)
    
    def onImportFinished(self, success_count: int, failed_count: int, skipped_count: int):
        """导入完成"""
        self.importBtn.setText("完成")
        self.importBtn.setEnabled(True)
        
        # 显示结果
        InfoBar.success(
            title="导入完成",
            content=f"成功: {success_count}, 失败: {failed_count}, 跳过: {skipped_count}",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=5000,
            parent=self.getMainInterface()
        )
        
        self.importCompleted.emit(success_count)
        
        # 延迟关闭对话框
        QTimer.singleShot(2000, self.accept)
