# coding: utf-8
"""
日志查看界面模块

该模块定义了日志查看界面，提供系统日志的查看、过滤和管理功能。

主要功能：
- 日志列表显示
- 日志级别过滤
- 日志搜索
- 日志导出

类说明：
- LogViewInterface: 日志查看界面类

作者: 小帅工具箱
版本: v1.0
"""

from PySide6.QtCore import Qt, QTimer, QDateTime
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QHeaderView, QTableWidgetItem, QFileDialog
from qfluentwidgets import (CardWidget, BodyLabel, SubtitleLabel, PushButton, PrimaryPushButton,
                            TableWidget, SearchLineEdit, ComboBox, DatePicker,
                            InfoBar, InfoBarPosition, MessageBox,
                            FluentIcon as FIF)

from ..database.dao import log_dao


class LogViewInterface(QWidget):
    """
    日志查看界面类
    
    提供系统日志的查看和管理功能。
    """
    
    def __init__(self, parent=None):
        """
        初始化日志查看界面
        
        Args:
            parent: 父窗口对象
        """
        super().__init__(parent=parent)
        self.setObjectName("LogViewInterface")
        
        # 日志数据
        self.logs = []
        self.filtered_logs = []

        # 过滤条件
        self.current_level_filter = "全部"
        self.current_search_text = ""
        self.current_date_filter = None

        # 初始化界面
        self.initUI()
        self.connectSignalToSlot()

        # 定时刷新
        self.refreshTimer = QTimer(self)
        self.refreshTimer.timeout.connect(self.loadLogs)
        self.refreshTimer.start(10000)  # 每10秒刷新一次

        # 加载数据
        self.loadLogs()
    
    def initUI(self):
        """初始化用户界面"""
        self.vBoxLayout = QVBoxLayout(self)
        self.vBoxLayout.setContentsMargins(0, 36, 0, 36)
        self.vBoxLayout.setSpacing(20)
        
        # 创建工具栏
        self.createToolbar()
        
        # 创建日志表格
        self.createLogTable()
    
    def createToolbar(self):
        """创建工具栏"""
        self.toolbarCard = CardWidget(self)
        self.toolbarLayout = QHBoxLayout(self.toolbarCard)
        self.toolbarLayout.setContentsMargins(20, 15, 20, 15)
        self.toolbarLayout.setSpacing(15)
        
        # 级别过滤
        self.levelCombo = ComboBox(self.toolbarCard)
        self.levelCombo.addItems(["全部", "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"])
        self.levelCombo.setCurrentText("全部")
        self.levelCombo.setFixedWidth(100)
        
        # 搜索框
        self.searchEdit = SearchLineEdit(self.toolbarCard)
        self.searchEdit.setPlaceholderText("搜索日志内容")
        self.searchEdit.setFixedWidth(250)
        
        # 按钮
        self.refreshBtn = PushButton("刷新", self.toolbarCard)
        self.refreshBtn.setIcon(FIF.SYNC)
        
        self.clearBtn = PushButton("清空日志", self.toolbarCard)
        self.clearBtn.setIcon(FIF.DELETE)
        
        self.exportBtn = PushButton("导出日志", self.toolbarCard)
        self.exportBtn.setIcon(FIF.SAVE)
        
        # 添加到布局
        self.toolbarLayout.addWidget(BodyLabel("级别:", self.toolbarCard))
        self.toolbarLayout.addWidget(self.levelCombo)
        self.toolbarLayout.addWidget(self.searchEdit)
        self.toolbarLayout.addStretch()
        self.toolbarLayout.addWidget(self.refreshBtn)
        self.toolbarLayout.addWidget(self.clearBtn)
        self.toolbarLayout.addWidget(self.exportBtn)
        
        self.vBoxLayout.addWidget(self.toolbarCard)
    
    def createLogTable(self):
        """创建日志表格"""
        self.tableCard = CardWidget(self)
        self.tableLayout = QVBoxLayout(self.tableCard)
        self.tableLayout.setContentsMargins(20, 20, 20, 20)
        self.tableLayout.setSpacing(15)

        # 表格标题
        self.tableTitle = SubtitleLabel("系统日志", self.tableCard)
        self.tableLayout.addWidget(self.tableTitle)

        # 日志表格
        self.logTable = TableWidget(self.tableCard)
        self.logTable.setColumnCount(5)
        self.logTable.setHorizontalHeaderLabels([
            "时间", "级别", "模块", "消息", "详细信息"
        ])

        # 设置表格属性
        self.logTable.setAlternatingRowColors(True)
        self.logTable.setSelectionBehavior(self.logTable.SelectionBehavior.SelectRows)
        self.logTable.horizontalHeader().setStretchLastSection(True)

        # 设置列宽
        self.logTable.setColumnWidth(0, 150)  # 时间
        self.logTable.setColumnWidth(1, 80)   # 级别
        self.logTable.setColumnWidth(2, 120)  # 模块
        self.logTable.setColumnWidth(3, 300)  # 消息

        # 设置行高
        self.logTable.verticalHeader().setDefaultSectionSize(30)

        self.tableLayout.addWidget(self.logTable)
        self.vBoxLayout.addWidget(self.tableCard)

    def loadLogs(self):
        """加载日志数据"""
        try:
            # 从数据库获取日志
            self.logs = log_dao.get_all_logs()
            self.applyFilters()

        except Exception as e:
            print(f"加载日志失败: {e}")

    def applyFilters(self):
        """应用过滤条件"""
        try:
            self.filtered_logs = []

            for log in self.logs:
                # 级别过滤
                if self.current_level_filter != "全部" and log.level != self.current_level_filter:
                    continue

                # 搜索过滤
                if self.current_search_text:
                    search_text = self.current_search_text.lower()
                    if (search_text not in log.message.lower() and
                        search_text not in log.module.lower() and
                        search_text not in (log.details or "").lower()):
                        continue

                self.filtered_logs.append(log)

            # 更新表格显示
            self.updateLogTable()

        except Exception as e:
            print(f"应用过滤条件失败: {e}")

    def updateLogTable(self):
        """更新日志表格"""
        try:
            # 清空表格
            self.logTable.setRowCount(0)

            # 添加日志数据
            for i, log in enumerate(self.filtered_logs):
                self.logTable.insertRow(i)

                # 时间
                time_str = ""
                if log.created_at:
                    try:
                        if hasattr(log.created_at, 'strftime'):
                            time_str = log.created_at.strftime('%Y-%m-%d %H:%M:%S')
                        else:
                            time_str = str(log.created_at)
                    except Exception:
                        time_str = str(log.created_at)
                self.logTable.setItem(i, 0, QTableWidgetItem(time_str))

                # 级别
                level_item = QTableWidgetItem(log.level)
                # 根据级别设置颜色
                if log.level == "ERROR" or log.level == "CRITICAL":
                    level_item.setForeground(Qt.GlobalColor.red)
                elif log.level == "WARNING":
                    level_item.setForeground(Qt.GlobalColor.darkYellow)
                elif log.level == "INFO":
                    level_item.setForeground(Qt.GlobalColor.blue)
                self.logTable.setItem(i, 1, level_item)

                # 模块
                self.logTable.setItem(i, 2, QTableWidgetItem(log.module))

                # 消息
                self.logTable.setItem(i, 3, QTableWidgetItem(log.message))

                # 详细信息
                details = log.details or ""
                self.logTable.setItem(i, 4, QTableWidgetItem(details))

            # 滚动到底部显示最新日志
            if self.logTable.rowCount() > 0:
                self.logTable.scrollToBottom()

        except Exception as e:
            print(f"更新日志表格失败: {e}")

    def onLevelFilterChanged(self):
        """级别过滤变化"""
        self.current_level_filter = self.levelCombo.currentText()
        self.applyFilters()

    def onSearchTextChanged(self):
        """搜索文本变化"""
        self.current_search_text = self.searchEdit.text()
        self.applyFilters()

    def onRefreshClicked(self):
        """刷新按钮点击"""
        self.loadLogs()
        InfoBar.success(
            title="成功",
            content="日志已刷新",
            orient=Qt.Orientation.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=2000,
            parent=self
        )

    def onClearClicked(self):
        """清空日志按钮点击"""
        try:
            # 确认对话框
            msg_box = MessageBox(
                title="确认清空",
                content="确定要清空所有日志吗？此操作不可恢复。",
                parent=self
            )

            if msg_box.exec():
                # 清空数据库中的日志
                if log_dao.clear_all_logs():
                    self.loadLogs()
                    InfoBar.success(
                        title="成功",
                        content="日志已清空",
                        orient=Qt.Orientation.Horizontal,
                        isClosable=True,
                        position=InfoBarPosition.TOP_RIGHT,
                        duration=2000,
                        parent=self
                    )
                else:
                    InfoBar.error(
                        title="错误",
                        content="清空日志失败",
                        orient=Qt.Orientation.Horizontal,
                        isClosable=True,
                        position=InfoBarPosition.TOP_RIGHT,
                        duration=3000,
                        parent=self
                    )

        except Exception as e:
            InfoBar.error(
                title="错误",
                content=f"清空日志失败: {str(e)}",
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=3000,
                parent=self
            )

    def onExportClicked(self):
        """导出日志按钮点击"""
        try:
            # 选择保存文件
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "导出日志",
                f"学习工具日志_{QDateTime.currentDateTime().toString('yyyyMMdd_hhmmss')}.csv",
                "CSV文件 (*.csv);;文本文件 (*.txt);;所有文件 (*)"
            )

            if file_path:
                # 导出日志
                if self.exportLogs(file_path):
                    InfoBar.success(
                        title="成功",
                        content=f"日志已导出到: {file_path}",
                        orient=Qt.Orientation.Horizontal,
                        isClosable=True,
                        position=InfoBarPosition.TOP_RIGHT,
                        duration=3000,
                        parent=self
                    )
                else:
                    InfoBar.error(
                        title="错误",
                        content="导出日志失败",
                        orient=Qt.Orientation.Horizontal,
                        isClosable=True,
                        position=InfoBarPosition.TOP_RIGHT,
                        duration=3000,
                        parent=self
                    )

        except Exception as e:
            InfoBar.error(
                title="错误",
                content=f"导出日志失败: {str(e)}",
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=3000,
                parent=self
            )

    def exportLogs(self, file_path: str) -> bool:
        """
        导出日志到文件

        Args:
            file_path: 文件路径

        Returns:
            bool: 是否成功
        """
        try:
            import csv

            with open(file_path, 'w', newline='', encoding='utf-8') as file:
                if file_path.endswith('.csv'):
                    writer = csv.writer(file)
                    # 写入表头
                    writer.writerow(['时间', '级别', '模块', '消息', '详细信息'])

                    # 写入数据
                    for log in self.filtered_logs:
                        time_str = ""
                        if log.created_at:
                            try:
                                if hasattr(log.created_at, 'strftime'):
                                    time_str = log.created_at.strftime('%Y-%m-%d %H:%M:%S')
                                else:
                                    time_str = str(log.created_at)
                            except Exception:
                                time_str = str(log.created_at)
                        writer.writerow([
                            time_str,
                            log.level,
                            log.module,
                            log.message,
                            log.details or ""
                        ])
                else:
                    # 文本格式
                    for log in self.filtered_logs:
                        time_str = ""
                        if log.created_at:
                            try:
                                if hasattr(log.created_at, 'strftime'):
                                    time_str = log.created_at.strftime('%Y-%m-%d %H:%M:%S')
                                else:
                                    time_str = str(log.created_at)
                            except Exception:
                                time_str = str(log.created_at)
                        file.write(f"[{time_str}] [{log.level}] [{log.module}] {log.message}\n")
                        if log.details:
                            file.write(f"详细信息: {log.details}\n")
                        file.write("\n")

            return True

        except Exception as e:
            print(f"导出日志失败: {e}")
            return False

    def connectSignalToSlot(self):
        """连接信号到槽函数"""
        self.levelCombo.currentTextChanged.connect(self.onLevelFilterChanged)
        self.searchEdit.textChanged.connect(self.onSearchTextChanged)
        self.refreshBtn.clicked.connect(self.onRefreshClicked)
        self.clearBtn.clicked.connect(self.onClearClicked)
        self.exportBtn.clicked.connect(self.onExportClicked)
    


