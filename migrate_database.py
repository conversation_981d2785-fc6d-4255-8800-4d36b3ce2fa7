# coding: utf-8
"""
数据库迁移脚本

该脚本用于更新数据库表结构，添加缺失的字段。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.xueyuan.database.models import db_manager

def migrate_database():
    """迁移数据库"""
    print("开始数据库迁移...")
    
    try:
        with db_manager.get_connection() as conn:
            # 检查 logs 表是否存在 details 列
            cursor = conn.execute("PRAGMA table_info(logs)")
            columns = [row[1] for row in cursor.fetchall()]
            
            print(f"当前 logs 表的列: {columns}")
            
            if 'details' not in columns:
                print("添加 details 列到 logs 表...")
                conn.execute("ALTER TABLE logs ADD COLUMN details TEXT")
                conn.commit()
                print("details 列添加成功!")
            else:
                print("details 列已存在，无需添加")
            
            # 验证表结构
            cursor = conn.execute("PRAGMA table_info(logs)")
            columns = [row[1] for row in cursor.fetchall()]
            print(f"更新后 logs 表的列: {columns}")
            
    except Exception as e:
        print(f"数据库迁移失败: {e}")
        return False
    
    print("数据库迁移完成!")
    return True

if __name__ == "__main__":
    migrate_database()
