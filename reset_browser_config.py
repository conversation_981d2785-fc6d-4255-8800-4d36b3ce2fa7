# coding: utf-8
"""
重置浏览器配置脚本

该脚本用于重置浏览器配置为正确的值。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.xueyuan.common.config import cfg

def reset_browser_config():
    """重置浏览器配置"""
    print("重置浏览器配置...")
    
    try:
        # 设置正确的浏览器类型
        cfg.set(cfg.browserType, "chromium")
        print(f"浏览器类型设置为: {cfg.browserType.value}")
        
        # 保存配置
        cfg.save()
        print("配置已保存")
        
    except Exception as e:
        print(f"重置配置失败: {e}")
        return False
    
    print("浏览器配置重置完成!")
    return True

if __name__ == "__main__":
    reset_browser_config()
