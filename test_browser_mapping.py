# coding: utf-8
"""
测试浏览器类型映射功能

该脚本用于验证浏览器类型映射是否正确工作。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.xueyuan.common.config import cfg

def test_browser_mapping():
    """测试浏览器类型映射"""
    print("测试浏览器类型映射功能...")
    
    # 浏览器类型映射
    browser_type_mapping = {
        'chrome': 'chromium',
        'firefox': 'firefox', 
        'edge': 'chromium',
        'webkit': 'webkit'
    }
    
    # 测试不同的配置值
    test_cases = ['chrome', 'firefox', 'edge', 'webkit']
    
    for browser_type in test_cases:
        # 设置配置
        cfg.set(cfg.browserType, browser_type)
        
        # 获取映射后的值
        browser_type_config = cfg.browserType.value.lower()
        playwright_browser_type = browser_type_mapping.get(browser_type_config, 'chromium')
        
        print(f"用户选择: {browser_type} -> Playwright API: {playwright_browser_type}")
    
    print("\n浏览器类型映射测试完成!")
    print("现在用户可以选择 'Chrome'，系统会自动映射到 Playwright 的 'chromium' API")

if __name__ == "__main__":
    test_browser_mapping()
