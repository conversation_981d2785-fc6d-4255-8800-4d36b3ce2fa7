# coding: utf-8
"""
测试日志修复的脚本

该脚本用于测试日志系统的修复是否有效。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtCore import QCoreApplication
from app.xueyuan.core.log_handler import global_log_handler
from app.xueyuan.database.dao import log_dao
from app.xueyuan.database.models import LogEntry

def test_log_system():
    """测试日志系统"""
    print("开始测试日志系统...")
    
    # 创建应用程序实例
    app = QCoreApplication(sys.argv)
    
    # 测试直接写入日志
    print("1. 测试直接写入日志...")
    test_log = LogEntry(
        level="INFO",
        message="测试日志消息",
        module="TestModule",
        user_phone="13800138000"
    )
    
    success = log_dao.create_log(test_log)
    print(f"直接写入日志结果: {success}")
    
    # 测试通过全局日志处理器写入
    print("2. 测试通过全局日志处理器写入...")
    global_log_handler.log_message("INFO", "通过全局处理器的测试消息", "GlobalHandler", "13800138001")
    
    # 处理事件循环以确保信号被处理
    app.processEvents()
    
    # 读取日志验证
    print("3. 验证日志是否写入成功...")
    logs = log_dao.get_all_logs(limit=10)
    print(f"数据库中的日志数量: {len(logs)}")
    
    for log in logs[-2:]:  # 显示最后两条日志
        print(f"日志: [{log.level}] [{log.module}] [{log.user_phone}] {log.message} - {log.created_at}")
    
    print("日志系统测试完成!")

if __name__ == "__main__":
    test_log_system()
