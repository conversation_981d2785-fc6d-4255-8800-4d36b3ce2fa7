# 学习工具开发设计文档

**项目名称**: 自动化学习辅助系统  
**基于框架**: PySide6-Fluent-Widgets  
**文档版本**: v1.0  
**创建日期**: 2025-01-14  

## 1. 项目分析阶段

### 1.1 现有脚手架结构分析

#### 核心目录结构
```
app/
├── common/           # 公共模块
│   ├── config.py    # 配置管理系统
│   ├── style_sheet.py # 样式表管理
│   ├── signal_bus.py # 信号总线
│   └── translator.py # 国际化支持
├── view/            # 界面模块
│   ├── main_window.py # 主窗口
│   ├── home_interface.py # 首页
│   ├── blank_interface.py # 空白页模板
│   └── setting_interface.py # 设置页
├── components/      # 自定义组件
└── config/         # 配置文件
    └── config.json # 应用配置
```

#### 现有配置系统特点
- 基于`QConfig`的配置管理
- 支持主题切换、DPI缩放、语言设置
- 配置文件自动序列化/反序列化
- 实时配置更新机制
- 统一入口为main.py
- 共用`app\common\style_sheet.py`、`app\view\main_window.py`、`app\resource\resource.qrc`等公共模块

#### 现有界面组件模式
- 继承自`MSFluentWindow`的主窗口
- 使用`ScrollArea`的滚动界面
- 统一的样式表管理系统
- 信号槽机制的事件处理

#### 新增开发模式规划
- 根据Fluent风格重新设计自定义控件和布局，使用`QConfig`的配置信息
- 新的界面采用`QSettings`管理UI状态，可跨界面保存状态。
- 根据项目目录结构和功能需求设置不同的样式信息
- 新开发的组件采用`qss`的样式表和自定义信号槽处理
- 组件的继承、组合和引用
- 新开发的组件无需国际化支持
- 学习工具设置配置独立于系统全局配置，需要单独维护
- `MSFluentButton`、`MSFluentLineEdit`和`MSFluentComboBox`等自定义控件继承`MSFluentWidget`，在原有基础上新增了统一的风格配置、样式和部分信号槽。
- 设置卡没有输入卡片样式，使用SettingsCard+LineEdit和SettingsCard+PasswordLineEdit方式实现。
- FluentIcon图标数量有限，图标使用FluentIcon现有图标（ACCEPT、ACCEPT_MEDIUM、ADD、ADD_TO、AIRPLANE、ALBUM、ALIGNMENT、APPLICATION、ARROW_DOWN、ASTERISK、BACKGROUND_FILL、BACK_TO_WINDOW、BASKETBALL、BLUETOOTH、BRIGHTNESS、BROOM、BRUSH、BUS、CAFE、CALENDAR、CALORIES、CAMERA、CANCEL、CANCEL_MEDIUM、CAR、CARE_DOWN_SOLID、CARE_LEFT_SOLID、CARE_RIGHT_SOLID、CARE_UP_SOLID、CERTIFICATE、CHAT、CHECKBOX、CHEVRON_DOWN_MED、CHEVRON_RIGHT、CHEVRON_RIGHT_MED、CLEAR_SELECTION、CLIPPING_TOOL、CLOSE、CLOUD、CLOUD_DOWNLOAD、CODE、COMMAND_PROMPT、COMPLETED、CONNECT、CONSTRACT、COPY、CUT、DATE_TIME、DELETE、DEVELOPER_TOOLS、DICTIONARY、DICTIONARY_ADD、DOCUMENT、DOWN、DOWNLOAD、EDIT、EDUCATION、EMBED、EMOJI_TAB_SYMBOLS、ERASE_TOOL、EXPRESSIVE_INPUT_ENTRY、FEEDBACK、FILTER、FINGERPRINT、FIT_PAGE、FLAG、FOLDER、FOLDER_ADD、FONT、FONT_INCREASE、FONT_SIZE、FRIGID、FULL_SCREEN、GAME、GITHUB、GLOBE、HEADPHONE、HEART、HELP、HIDE、HIGHTLIGHT、HISTORY、HOME、HOME_FILL、IMAGE_EXPORT、INFO、IOT、LABEL、LANGUAGE、LAYOUT、LEAF、LEFT_ARROW、LIBRARY、LIBRARY_FILL、LINK、MAIL、MARKET、MEDIA、MEGAPHONE、MENU、MESSAGE、MICROPHONE、MINIMIZE、MIX_VOLUMES、MORE、MOVE、MOVIE、MUSIC、MUSIC_FOLDER、MUTE、PAGE_LEFT、PAGE_RIGHT、PALETTE、PASTE、PAUSE、PAUSE_BOLD、PENCIL_INK、PEOPLE、PHONE、PHOTO、PIE_SINGLE、PIN、PLAY、PLAY_SOLID、POWER_BUTTON、PRINT、PROJECTOR、QRCODE、QUESTION、QUICK_NOTE、QUIET_HOURS、REMOVE、REMOVE_FROM、RETURN、RIGHT_ARROW、RINGER、ROBOT、ROTATE、SAVE、SAVE_AS、SAVE_COPY、SCROLL、SEARCH、SEARCH_MIRROR、SEND、SEND_FILL、SETTING、SHARE、SHOPPING_CART、SKIP_BACK、SKIP_FORWARD、SPEAKERS、SPEED_HIGH、SPEED_MEDIUM、SPEED_OFF、STOP_WATCH、SYNC、TAG、TILES、TRAIN、TRANSPARENT、UNIT、UNPIN、UP、UPDATE、VIDEO、VIEW、VOLUME、VPN、WIFI、ZIP_FOLDER、ZOOM、ZOOM_IN、ZOOM_OUT）

### 1.2 扩展模块规划

#### 新增目录结构
```
app/
├── xueyuan/         # 学习工具模块
│   ├── common/      # 学习工具公共模块
│   ├── components/  # 学习工具界面组件
│   ├── view/        # 学习工具界面
│   ├── core/        # 核心业务逻辑
│   └── database/    # 数据库操作
├── ocr/             # OCR识别模块
│   ├── ddddocr/     # Ddddocr引擎
│   ├── baidu/       # 百度OCR引擎
│   └── common/      # OCR公共模块
└── data/            # 数据目录
    ├── config/      # 配置文件
    └── database/    # 数据库文件
```

## 2. 需求分析

### 2.1 功能模块划分

#### 核心功能模块
1. **用户管理模块**
   - 批量导入用户账号
   - 用户信息编辑
   - 状态管理和统计

2. **自动化学习模块**
   - Playwright浏览器自动化
   - 登录流程自动化
   - 课程学习自动化
   - 视频播放监控

3. **OCR识别模块**
   - Ddddocr主引擎
   - 百度OCR备选引擎
   - 验证码识别和处理

4. **数据管理模块**
   - SQLite3数据存储
   - 用户数据管理
   - 课程数据管理
   - 学习记录管理

5. **日志系统模块**
   - 多级别日志记录
   - 实时日志查看
   - 日志导出和清理

6. **线程池模块**
   - 并发控制
   - 任务队列管理
   - 异常处理和重试

### 2.2 数据结构设计

#### 用户数据表 (users)
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    phone TEXT UNIQUE NOT NULL,           -- 手机号码
    password TEXT NOT NULL,               -- 密码
    name TEXT,                           -- 姓名
    status TEXT DEFAULT '未开始',         -- 状态
    complete_status TEXT DEFAULT '1',     -- 完成状态
    online_total_credit REAL DEFAULT 0,  -- 总学时
    compulsory_credit REAL DEFAULT 0,    -- 必修学时
    electives_credit REAL DEFAULT 0,     -- 选修学时
    last_login_time DATETIME,            -- 最后登录时间
    error_message TEXT,                  -- 错误信息
    progress REAL DEFAULT 0,             -- 进度
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 课程数据表 (courses)
```sql
CREATE TABLE courses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_phone TEXT NOT NULL,            -- 用户手机号
    course_type TEXT NOT NULL,           -- 课程类型(必修课/选修课)
    name TEXT NOT NULL,                  -- 课程名称
    course_id TEXT NOT NULL,             -- 课程ID
    completed TEXT DEFAULT '0',          -- 完成状态
    credit INTEGER DEFAULT 0,            -- 学时
    percentage TEXT DEFAULT '0',         -- 完成进度
    courseware_id TEXT,                  -- 课件ID
    video_url TEXT,                      -- 视频URL
    completed_date DATE,                 -- 完成日期
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_phone) REFERENCES users(phone)
);
```

#### 日志数据表 (logs)
```sql
CREATE TABLE logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    level TEXT NOT NULL,                 -- 日志级别
    message TEXT NOT NULL,               -- 日志消息
    module TEXT,                         -- 模块名称
    user_phone TEXT,                     -- 相关用户
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### API数据表 (api_data)
```sql
CREATE TABLE api_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_phone TEXT NOT NULL,            -- 用户手机号
    api_url TEXT NOT NULL,               -- API地址
    request_method TEXT DEFAULT 'GET',   -- 请求方法
    response_status INTEGER,             -- 响应状态码
    response_data TEXT,                  -- 响应数据(JSON格式)
    capture_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_processed BOOLEAN DEFAULT FALSE,  -- 是否已处理
    error_message TEXT,                  -- 错误信息
    FOREIGN KEY (user_phone) REFERENCES users(phone)
);
```

## 3. 技术方案设计

### 3.1 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    主界面 (MainWindow)                        │
├─────────────────────────────────────────────────────────────┤
│  学习控制  │  用户管理  │  进度监控  │  日志查看  │  学习设置  │
├─────────────────────────────────────────────────────────────┤
│                    业务逻辑层                                │
├─────────────────────────────────────────────────────────────┤
│  学习引擎  │  OCR引擎  │  数据管理  │  线程池   │  日志系统  │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层                                │
├─────────────────────────────────────────────────────────────┤
│  SQLite3  │  配置文件  │  日志文件  │  临时数据  │  缓存数据  │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 核心模块设计

#### 3.2.1 学习配置系统
```python
class StudyConfig(QConfig):
    # 系统配置
    asyncLogin = ConfigItem("System", "AsyncLogin", True, BoolValidator())
    compulsoryCourses = RangeConfigItem("System", "CompulsoryCourses", 20, RangeValidator(1, 100))
    electiveCourses = RangeConfigItem("System", "ElectiveCourses", 20, RangeValidator(1, 100))
    concurrentCount = RangeConfigItem("System", "ConcurrentCount", 2, RangeValidator(1, 10))
    
    # 浏览器配置
    browserType = OptionsConfigItem("Browser", "BrowserType", "chrome", OptionsValidator(["chrome", "firefox", "edge"]))
    headless = ConfigItem("Browser", "Headless", False, BoolValidator())
    
    # OCR配置
    ocrApiKey = ConfigItem("OCR", "ApiKey", "", str)
    ocrSecretKey = ConfigItem("OCR", "SecretKey", "", str)

    # API配置
    apiTimeout = RangeConfigItem("API", "Timeout", 30, RangeValidator(5, 120))
    apiRetryCount = RangeConfigItem("API", "RetryCount", 3, RangeValidator(1, 10))
    apiCaptureEnabled = ConfigItem("API", "CaptureEnabled", True, BoolValidator())

    # 网站配置
    baseUrl = ConfigItem("Website", "BaseUrl", "https://study.jxgbwlxy.gov.cn", str)
    loginUrl = ConfigItem("Website", "LoginUrl", "https://study.jxgbwlxy.gov.cn/index", str)

    # 日志配置
    logLevel = OptionsConfigItem("Logging", "LogLevel", "INFO", OptionsValidator(["DEBUG", "INFO", "WARNING", "ERROR"]))
```

#### 3.2.2 自动化学习引擎
```python
class StudyEngine:
    """学习引擎核心类"""

    def __init__(self):
        self.api_capture = APICapture()
        self.page = None
        self.browser = None

    async def login(self, phone: str, password: str) -> bool:
        """自动登录"""
        # 设置API监听器
        self.page.on("response", self.api_capture.handle_response)

        # 执行登录流程
        await self.page.goto("https://study.jxgbwlxy.gov.cn/index")
        await self.page.fill('.el-input__inner[placeholder="您的手机号"]', phone)
        await self.page.fill('.el-input__inner[placeholder="请输入密码"]', password)

        # 处理验证码
        captcha = await self.handle_captcha()
        await self.page.fill('.el-input__inner[placeholder="验证码"]', captcha)

        # 提交登录
        await self.page.click('.loginBtn.el-button')
        await self.page.wait_for_url("**/study/data", timeout=30000)

        # 等待API数据捕获
        await self.page.wait_for_load_state("networkidle")
        await asyncio.sleep(3)

        return True

    async def get_user_status(self) -> dict:
        """获取用户学习状态"""
        # 从API捕获器获取数据
        return self.api_capture.get_user_status_data()

    async def get_student_info(self) -> dict:
        """获取学生信息"""
        return self.api_capture.get_student_info_data()

    async def get_courses(self, course_type: str = "必修课") -> dict:
        """获取课程列表"""
        if course_type == "必修课":
            return await self.get_compulsory_courses()
        else:
            return await self.get_elective_courses()

    async def get_compulsory_courses(self) -> list:
        """获取必修课程列表"""
        await self.page.goto("https://study.jxgbwlxy.gov.cn/study/courseMine?id=0")
        await self.page.click("text=我的必修课")

        courses = []
        current_page = 1

        while True:
            # 等待API响应
            await self.page.wait_for_load_state("networkidle")

            # 从API捕获器获取课程数据
            api_data = self.api_capture.get_compulsory_courses_data()
            if api_data and 'records' in api_data:
                for course in api_data['records']:
                    courses.append({
                        "name": course.get("name", ""),
                        "completed": course.get("completed", "0"),
                        "id": course.get("id", ""),
                        "credit": course.get("credit", 0),
                        "percentage": course.get("percentage", "0"),
                        "coursewareId": course.get("courseware", {}).get("id", ""),
                        "videoUrl": f"https://study.jxgbwlxy.gov.cn/video?id={course.get('courseware', {}).get('id', '')}",
                        "completed_date": ""
                    })

                # 检查是否有下一页
                if api_data.get('current', 1) < api_data.get('pages', 1):
                    await self.page.click('.btn-next')
                    current_page += 1
                else:
                    break
            else:
                break

        return courses

    async def get_elective_courses(self) -> list:
        """获取选修课程列表"""
        await self.page.goto("https://study.jxgbwlxy.gov.cn/study/courseMine?id=0")
        await self.page.click("text=我的选修课")

        courses = []
        current_page = 1

        while True:
            # 等待API响应
            await self.page.wait_for_load_state("networkidle")

            # 从API捕获器获取课程数据
            api_data = self.api_capture.get_elective_courses_data()
            if api_data and 'records' in api_data:
                for course in api_data['records']:
                    courses.append({
                        "name": course.get("name", ""),
                        "completed": course.get("completed", "0"),
                        "id": course.get("id", ""),
                        "credit": course.get("credit", 0),
                        "percentage": course.get("percentage", "0"),
                        "coursewareId": course.get("courseware", {}).get("id", ""),
                        "videoUrl": f"https://study.jxgbwlxy.gov.cn/video?id={course.get('courseware', {}).get('id', '')}",
                        "completed_date": ""
                    })

                # 检查是否有下一页
                if api_data.get('current', 1) < api_data.get('pages', 1):
                    await self.page.click('.btn-next')
                    current_page += 1
                else:
                    break
            else:
                break

        return courses

    async def add_elective_courses(self, required_count: int) -> bool:
        """自动添加选修课程"""
        await self.page.goto("https://study.jxgbwlxy.gov.cn/study/course-view")
        await self.page.wait_for_load_state("networkidle")

        # 检查页面模式并切换到表格模式
        grid_button = await self.page.query_selector('.el-button--danger.el-button--small.is-plain .el-icon-s-grid')
        if grid_button:
            await self.page.click('.el-button--danger.el-button--small.is-plain')
            await self.page.wait_for_timeout(1000)

        added_count = 0
        current_page = 1

        while added_count < required_count:
            # 查找添加课程按钮
            add_buttons = await self.page.query_selector_all('.el-icon-circle-plus-outline')

            for button in add_buttons:
                if added_count >= required_count:
                    break

                await button.click()
                await self.page.wait_for_timeout(500)
                added_count += 1

            # 检查是否需要翻页
            if added_count < required_count:
                next_button = await self.page.query_selector('.btn-next:not(.disabled)')
                if next_button:
                    await next_button.click()
                    await self.page.wait_for_load_state("networkidle")
                    current_page += 1
                else:
                    break

        return added_count >= required_count

    async def study_course(self, course_data: dict) -> bool:
        """学习指定课程"""
        video_url = course_data.get("videoUrl", "")
        if not video_url:
            return False

        # 打开视频播放页面
        await self.page.goto(video_url)
        await self.page.wait_for_load_state("networkidle")

        # 处理温馨提示页面
        if "videoChoose" in self.page.url:
            choose_content = await self.page.query_selector('.choose-content')
            if choose_content:
                await choose_content.click()
                await self.page.wait_for_load_state("networkidle")

        # 更新用户学习状态
        await self.update_user_status_from_api()

        # 开始视频学习监控
        return await self.monitor_video_learning()

    async def monitor_video_learning(self) -> bool:
        """监控视频学习过程"""
        while True:
            # 检查是否有未完成的课件
            has_incomplete = await self.page.evaluate("""
                const elements = document.querySelectorAll('span[title]');
                return Array.from(elements).some(el => el.textContent.includes('未完成'));
            """)

            if not has_incomplete:
                # 所有课件已完成，更新课程状态
                return True

            # 等待一段时间后继续检查
            await asyncio.sleep(30)

    async def monitor_video_progress(self) -> dict:
        """监控视频播放进度"""
        try:
            # 获取视频播放时间和总时长
            video_info = await self.page.evaluate("""
                const video = document.querySelector('video');
                if (video) {
                    return {
                        currentTime: video.currentTime,
                        duration: video.duration,
                        paused: video.paused,
                        ended: video.ended
                    };
                }
                return null;
            """)

            if video_info:
                progress = 0
                if video_info['duration'] > 0:
                    progress = (video_info['currentTime'] / video_info['duration']) * 100

                return {
                    'currentTime': video_info['currentTime'],
                    'duration': video_info['duration'],
                    'progress': progress,
                    'paused': video_info['paused'],
                    'ended': video_info['ended']
                }

            return {}
        except Exception as e:
            print(f"获取视频进度失败: {e}")
            return {}

    async def handle_captcha(self) -> str:
        """处理验证码"""
        # 获取验证码图片
        captcha_element = await self.page.query_selector('.yzmImg')
        if captcha_element:
            image_data = await captcha_element.screenshot()
            # 使用OCR识别
            ocr_manager = OCRManager()
            return await ocr_manager.recognize_captcha(image_data)
        return ""

    async def capture_api_data(self) -> dict:
        """捕获API数据"""
        return self.api_capture.captured_data

    async def update_user_status_from_api(self) -> dict:
        """从API数据更新用户状态"""
        # 获取用户学习状态
        status_data = self.api_capture.get_user_status_data()
        student_data = self.api_capture.get_student_info_data()

        user_info = {}
        if status_data:
            user_info.update({
                'completeStatus': status_data.get('completeStatus', '1'),
                'onlineTotalCredit': status_data.get('onlineTotalCredit', '0'),
                'compulsoryCredit': status_data.get('compulsoryCredit', 0),
                'electivesCredit': status_data.get('electivesCredit', 0)
            })

        if student_data:
            user_info.update({
                'name': student_data.get('stuName', ''),
                'studentId': student_data.get('id', '')
            })

        return user_info

    async def check_study_completion(self) -> bool:
        """检查学习是否完成"""
        user_status = await self.update_user_status_from_api()
        complete_status = user_status.get('completeStatus', '1')
        return complete_status == '0'  # 0表示已完成，1表示未完成

    async def get_course_data_for_user(self, phone: str) -> dict:
        """获取用户的课程数据"""
        # 检查数据库中是否已有课程数据
        # 如果没有或数量不足，则重新获取
        compulsory_courses = await self.get_compulsory_courses()
        elective_courses = await self.get_elective_courses()

        return {
            "必修课": compulsory_courses,
            "选修课": elective_courses
        }

    async def start_learning_process(self, phone: str, password: str) -> bool:
        """开始完整的学习流程"""
        try:
            # 1. 登录
            if not await self.login(phone, password):
                return False

            # 2. 检查学习状态
            if await self.check_study_completion():
                print(f"用户 {phone} 学习已完成")
                return True

            # 3. 获取课程数据
            courses_data = await self.get_course_data_for_user(phone)

            # 4. 开始学习未完成的课程
            for course_type, courses in courses_data.items():
                for course in courses:
                    if course.get('completed', '0') == '0':  # 未完成的课程
                        print(f"开始学习课程: {course.get('name', '')}")
                        await self.study_course(course)

                        # 更新课程完成状态
                        course['completed'] = '1'
                        course['completed_date'] = datetime.now().strftime('%Y-%m-%d')

            return True

        except Exception as e:
            print(f"学习流程执行失败: {e}")
            return False
```

#### 3.2.3 OCR识别系统
```python
class OCRManager:
    """OCR管理器"""
    
    def __init__(self):
        self.ddddocr_engine = DdddocrEngine()
        self.baidu_engine = BaiduOCREngine()

    async def recognize_captcha(self, image_data: bytes) -> str:
        """识别验证码"""
        # 优先使用Ddddocr，失败时使用百度OCR
```

#### 3.2.4 线程池管理
```python
class StudyThreadPool:
    """学习线程池"""
    
    def __init__(self, max_workers: int = 2):
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.tasks = {}
        
    def submit_study_task(self, user_data: dict) -> Future:
        """提交学习任务"""
        
    def get_task_status(self, task_id: str) -> str:
        """获取任务状态"""
```

### 3.3 界面设计方案

#### 3.3.1 主界面布局
```python
class StudyMainInterface(ScrollArea):
    """学习工具主界面"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.pivot = Pivot(self)  # 顶部导航
        self.setupUI()
        
    def setupUI(self):
        # 添加各个子界面到Pivot
        self.addSubInterface(StudyControlInterface(), "学习控制")
        self.addSubInterface(UserManageInterface(), "用户管理")
        self.addSubInterface(ProgressMonitorInterface(), "进度监控")
        self.addSubInterface(LogViewInterface(), "日志查看")
        self.addSubInterface(StudySettingInterface(), "学习设置")
```

#### 3.3.2 用户管理界面
```python
class UserManageInterface(QWidget):
    """用户管理界面"""
    
    def setupUI(self):
        # 顶部操作栏
        self.toolbar = QHBoxLayout()
        self.addUserBtn = PrimaryPushButton("批量添加")
        self.importBtn = PushButton("导入用户")
        self.editBtn = PushButton("编辑用户")
        
        # 用户列表表格
        self.userTable = TableWidget()
        self.setupTable()
        
        # 底部统计栏
        self.statusBar = QHBoxLayout()
        self.totalLabel = BodyLabel("总用户: 0")
        self.waitingLabel = BodyLabel("待学习: 0")
```

#### 3.3.3 学习控制界面
```python
class StudyControlInterface(QWidget):
    """学习控制界面"""
    
    def setupUI(self):
        # 左侧控制区
        self.controlPanel = QVBoxLayout()
        self.progressRing = ProgressRing()  # 进度环
        self.statusLabel = BodyLabel("就绪")
        self.startBtn = PrimaryPushButton("启动学习")
        self.pauseBtn = PushButton("暂停学习")
        self.stopBtn = PushButton("停止学习")
        
        # 右侧进度区
        self.progressTable = TableWidget()
        self.setupProgressTable()
```
#### 3.4 配置类
```python
class StudyConfig(QConfig):
    """
    学习工具配置类

    定义学习工具的所有配置项，包括系统设置、浏览器配置、
    OCR识别、API捕获、网站配置和日志设置等各种配置选项。
    """

    # 系统配置
    asyncLogin = ConfigItem("System", "AsyncLogin", True, BoolValidator())
    compulsoryCourses = RangeConfigItem("System", "CompulsoryCourses", 20, RangeValidator(1, 100))
    electiveCourses = RangeConfigItem("System", "ElectiveCourses", 20, RangeValidator(1, 100))
    concurrentCount = RangeConfigItem("System", "ConcurrentCount", 2, RangeValidator(1, 10))
    delayTime = RangeConfigItem("System", "DelayTime", 1, RangeValidator(0, 10))
    retryCount = RangeConfigItem("System", "RetryCount", 3, RangeValidator(1, 10))
    autoStart = ConfigItem("System", "AutoStart", False, BoolValidator())
    autoMinimize = ConfigItem("System", "AutoMinimize", True, BoolValidator())
    enableNotifications = ConfigItem("System", "EnableNotifications", True, BoolValidator())
    saveWindowState = ConfigItem("System", "SaveWindowState", True, BoolValidator())
    checkUpdatesOnStart = ConfigItem("System", "CheckUpdatesOnStart", True, BoolValidator())

    # 浏览器配置
    browserType = OptionsConfigItem("Browser", "BrowserType", "chrome",
                                   OptionsValidator(["chrome", "firefox", "edge", "webkit"]))
    headless = ConfigItem("Browser", "Headless", False, BoolValidator())
    disableImages = ConfigItem("Browser", "DisableImages", False, BoolValidator())
    disableJavaScript = ConfigItem("Browser", "DisableJavaScript", False, BoolValidator())
    disableCSS = ConfigItem("Browser", "DisableCSS", False, BoolValidator())
    userAgent = ConfigItem("Browser", "UserAgent", "")
    windowWidth = RangeConfigItem("Browser", "WindowWidth", 1920, RangeValidator(800, 3840))
    windowHeight = RangeConfigItem("Browser", "WindowHeight", 1080, RangeValidator(600, 2160))
    pageLoadTimeout = RangeConfigItem("Browser", "PageLoadTimeout", 30, RangeValidator(5, 120))
    elementWaitTimeout = RangeConfigItem("Browser", "ElementWaitTimeout", 10, RangeValidator(1, 60))
    downloadPath = ConfigItem("Browser", "DownloadPath", "downloads")
    enableDevTools = ConfigItem("Browser", "EnableDevTools", False, BoolValidator())

    # OCR配置
    primaryEngine = OptionsConfigItem("OCR", "PrimaryEngine", "ddddocr",
                                     OptionsValidator(["ddddocr", "baidu", "tesseract", "paddleocr"]))
    fallbackEngine = OptionsConfigItem("OCR", "FallbackEngine", "baidu",
                                      OptionsValidator(["ddddocr", "baidu", "tesseract", "paddleocr", "none"]))
    baiduApiKey = ConfigItem("OCR", "BaiduApiKey", "")
    baiduSecretKey = ConfigItem("OCR", "BaiduSecretKey", "")
    ocrTimeout = RangeConfigItem("OCR", "Timeout", 10, RangeValidator(5, 60))
    ocrRetryCount = RangeConfigItem("OCR", "RetryCount", 3, RangeValidator(1, 10))
    enablePreprocessing = ConfigItem("OCR", "EnablePreprocessing", True, BoolValidator())
    imageEnhancement = ConfigItem("OCR", "ImageEnhancement", True, BoolValidator())
    confidenceThreshold = RangeConfigItem("OCR", "ConfidenceThreshold", 0.8, RangeValidator(0.1, 1.0))

    # API配置
    apiTimeout = RangeConfigItem("API", "Timeout", 30, RangeValidator(5, 120))
    apiRetryCount = RangeConfigItem("API", "RetryCount", 3, RangeValidator(1, 10))
    apiCaptureEnabled = ConfigItem("API", "CaptureEnabled", True, BoolValidator())

    # 学习策略配置
    studyMode = OptionsConfigItem("StudyStrategy", "StudyMode", "sequential",
                                 OptionsValidator(["sequential", "random", "priority"]))
    autoNextCourse = ConfigItem("StudyStrategy", "AutoNextCourse", True, BoolValidator())
    skipCompletedCourses = ConfigItem("StudyStrategy", "SkipCompletedCourses", True, BoolValidator())
    minStudyTimePerCourse = RangeConfigItem("StudyStrategy", "MinStudyTimePerCourse", 300, RangeValidator(60, 3600))  # 秒
    maxStudyTimePerCourse = RangeConfigItem("StudyStrategy", "MaxStudyTimePerCourse", 1800, RangeValidator(300, 7200))  # 秒
    studySpeedMultiplier = RangeConfigItem("StudyStrategy", "StudySpeedMultiplier", 1.0, RangeValidator(0.5, 3.0))
    enableRandomDelay = ConfigItem("StudyStrategy", "EnableRandomDelay", True, BoolValidator())
    randomDelayMin = RangeConfigItem("StudyStrategy", "RandomDelayMin", 1, RangeValidator(0, 30))  # 秒
    randomDelayMax = RangeConfigItem("StudyStrategy", "RandomDelayMax", 5, RangeValidator(1, 60))  # 秒

    # 日志配置
    logLevel = OptionsConfigItem("Logging", "LogLevel", "INFO",
                                OptionsValidator(["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]))
    logDir = ConfigItem("Logging", "LogDir", "logs")
    maxLogFileSize = RangeConfigItem("Logging", "MaxLogFileSize", 10485760, RangeValidator(1048576, 104857600))  # 1MB-100MB
    logBackupCount = RangeConfigItem("Logging", "LogBackupCount", 5, RangeValidator(1, 20))
    enableConsoleLog = ConfigItem("Logging", "EnableConsoleLog", True, BoolValidator())
    enableFileLog = ConfigItem("Logging", "EnableFileLog", True, BoolValidator())
    enableDatabaseLog = ConfigItem("Logging", "EnableDatabaseLog", True, BoolValidator())
    maxDays = RangeConfigItem("Logging", "MaxDays", 30, RangeValidator(1, 365))
    maxSizeMB = RangeConfigItem("Logging", "MaxSizeMB", 100, RangeValidator(1, 1000))

    # 并发控制配置
    maxConcurrentTasks = RangeConfigItem("Concurrency", "MaxConcurrentTasks", 10, RangeValidator(1, 50))
    maxConcurrentUsers = RangeConfigItem("Concurrency", "MaxConcurrentUsers", 5, RangeValidator(1, 20))
    maxWorkers = RangeConfigItem("Concurrency", "MaxWorkers", 4, RangeValidator(1, 16))
    maxQueueSize = RangeConfigItem("Concurrency", "MaxQueueSize", 100, RangeValidator(10, 1000))
    defaultTimeout = RangeConfigItem("Concurrency", "DefaultTimeout", 300, RangeValidator(30, 3600))
    apiRateLimit = RangeConfigItem("Concurrency", "ApiRateLimit", 60, RangeValidator(10, 1000))
    schedulerInterval = RangeConfigItem("Concurrency", "SchedulerInterval", 1.0, RangeValidator(0.1, 10.0))

    # 数据备份配置
    enableAutoBackup = ConfigItem("Backup", "EnableAutoBackup", True, BoolValidator())
    backupInterval = RangeConfigItem("Backup", "BackupInterval", 24, RangeValidator(1, 168))  # 小时
    maxBackupFiles = RangeConfigItem("Backup", "MaxBackupFiles", 10, RangeValidator(1, 100))
    backupPath = ConfigItem("Backup", "BackupPath", "backups")
    compressBackups = ConfigItem("Backup", "CompressBackups", True, BoolValidator())


# 创建学习工具配置实例
cfg = StudyConfig()
qconfig.load('cfg', "data/config/study.config")
```

## 4. 开发计划

### 4.1 开发阶段划分

#### 第一阶段：基础架构搭建 (3-4天)
1. **配置系统开发**
   - 创建`StudyConfig`配置类
   - 实现配置文件加载和保存
   - 集成到现有配置系统

2. **数据库系统开发**
   - 设计数据库表结构
   - 实现数据访问层(DAO)
   - 数据库初始化和迁移

3. **基础界面框架**
   - 创建学习工具主界面
   - 实现Pivot导航结构
   - 基础样式表开发

#### 第二阶段：核心功能开发 (5-6天)
1. **OCR识别模块**
   - Ddddocr引擎集成
   - 百度OCR引擎集成
   - OCR管理器实现

2. **自动化学习引擎**
   - Playwright浏览器自动化
   - 登录流程实现
   - API捕获系统集成
   - 状态检查模块

3. **用户管理功能**
   - 用户数据CRUD操作
   - 批量导入功能
   - 用户状态管理

#### 第三阶段：学习流程开发 (5-6天)
1. **自动登录模块**
   - 登录状态机实现
   - 验证码处理优化
   - 登录错误处理机制

2. **状态检查模块**
   - 用户状态检查器
   - API数据解析和更新
   - 学习完成判断逻辑

3. **课程学习流程**
   - 课程管理器实现
   - 必修课/选修课获取
   - 自动添加选修课功能

4. **视频监听模块**
   - 视频学习监控器
   - 播放进度跟踪
   - 课件完成检测

5. **线程池和并发控制**
   - 多任务并发执行
   - 任务队列管理
   - 异常处理和重试

#### 第四阶段：界面完善和测试 (3-4天)
1. **界面优化**
   - 用户体验优化
   - 响应式布局调整
   - 主题适配完善

2. **功能测试**
   - 单元测试编写
   - 集成测试
   - 性能测试

3. **文档和部署**
   - 用户手册编写
   - 部署脚本准备
   - 版本发布准备

### 4.2 开发优先级

#### 高优先级 (P0)
- 配置系统和数据库
- API捕获系统
- OCR识别功能
- 基础自动化登录
- 用户管理界面

#### 中优先级 (P1)
- 课程学习流程
- 线程池并发控制
- 日志系统
- 进度监控界面

#### 低优先级 (P2)
- 界面美化优化
- 高级配置选项
- 性能优化
- 扩展功能

### 4.3 工作量预估

| 模块 | 预估工时 | 复杂度 | 风险等级 |
|------|----------|--------|----------|
| 配置系统 | 1天 | 低 | 低 |
| 数据库设计 | 1天 | 中 | 低 |
| API捕获系统 | 2天 | 中 | 中 |
| OCR识别 | 2天 | 中 | 中 |
| 自动登录模块 | 2天 | 中 | 中 |
| 状态检查模块 | 1天 | 低 | 低 |
| 课程管理器 | 2天 | 中 | 中 |
| 视频监听模块 | 3天 | 高 | 高 |
| 用户管理 | 2天 | 中 | 低 |
| 线程池 | 2天 | 中 | 中 |
| 日志系统 | 1天 | 低 | 低 |
| 界面开发 | 3天 | 中 | 低 |
| 测试优化 | 2天 | 中 | 中 |

**总计**: 24-26个工作日

## 5. 风险评估和应对策略

### 5.1 技术风险

#### 高风险项
1. **网站反爬虫机制**
   - 风险: 目标网站可能有反自动化检测
   - 应对: 实现随机延迟、User-Agent轮换、代理支持

2. **验证码识别准确率**
   - 风险: OCR识别失败率较高
   - 应对: 双引擎备份、人工介入机制

3. **网站结构变更**
   - 风险: 目标网站DOM结构改变
   - 应对: 灵活的选择器配置、多重定位策略

#### 中风险项
1. **并发控制复杂性**
   - 风险: 多线程同步问题
   - 应对: 使用成熟的线程池库、充分测试

2. **数据一致性**
   - 风险: 并发操作导致数据不一致
   - 应对: 数据库事务、锁机制

### 5.2 业务风险

1. **合规性风险**
   - 风险: 自动化学习可能违反平台规则
   - 应对: 添加免责声明、用户自主选择

2. **性能风险**
   - 风险: 大量并发可能影响目标服务器
   - 应对: 合理的并发限制、请求频率控制

## 6. 学习流程设计

### 6.1 完整学习流程架构

```
学习流程总览:
┌─────────────────────────────────────────────────────────────┐
│                    启动学习任务                              │
├─────────────────────────────────────────────────────────────┤
│  1. 自动登录模块  →  2. 状态检查模块  →  3. 课程学习流程模块  │
├─────────────────────────────────────────────────────────────┤
│                    4. 视频监听模块                           │
├─────────────────────────────────────────────────────────────┤
│                    5. API捕获与数据更新                      │
└─────────────────────────────────────────────────────────────┘
```

### 6.2 自动登录模块设计

#### 6.2.1 登录流程状态机
```python
class LoginState(Enum):
    INIT = "初始化"
    LOADING_PAGE = "加载页面"
    FILLING_CREDENTIALS = "填写凭据"
    HANDLING_CAPTCHA = "处理验证码"
    SUBMITTING = "提交登录"
    VERIFYING = "验证登录"
    SUCCESS = "登录成功"
    FAILED = "登录失败"

class LoginModule:
    """自动登录模块"""

    def __init__(self, study_engine):
        self.engine = study_engine
        self.state = LoginState.INIT
        self.retry_count = 0
        self.max_retries = 3

    async def execute_login(self, phone: str, password: str) -> bool:
        """执行登录流程"""
        self.state = LoginState.LOADING_PAGE

        try:
            # 1. 加载登录页面
            await self.engine.page.goto("https://study.jxgbwlxy.gov.cn/index")
            await self.engine.page.wait_for_load_state("networkidle")

            # 2. 填写账号密码
            self.state = LoginState.FILLING_CREDENTIALS
            await self.fill_credentials(phone, password)

            # 3. 处理验证码
            self.state = LoginState.HANDLING_CAPTCHA
            captcha_result = await self.handle_captcha_with_retry()
            if not captcha_result:
                self.state = LoginState.FAILED
                return False

            # 4. 提交登录
            self.state = LoginState.SUBMITTING
            await self.engine.page.click('.loginBtn.el-button')

            # 5. 验证登录结果
            self.state = LoginState.VERIFYING
            success = await self.verify_login_success()

            self.state = LoginState.SUCCESS if success else LoginState.FAILED
            return success

        except Exception as e:
            print(f"登录过程出错: {e}")
            self.state = LoginState.FAILED
            return False

    async def fill_credentials(self, phone: str, password: str):
        """填写登录凭据"""
        await self.engine.page.fill('.el-input__inner[placeholder="您的手机号"]', phone)
        await self.engine.page.fill('.el-input__inner[placeholder="请输入密码"]', password)

    async def handle_captcha_with_retry(self) -> bool:
        """带重试的验证码处理"""
        for attempt in range(self.max_retries):
            try:
                captcha = await self.engine.handle_captcha()
                if captcha and len(captcha) == 4:
                    await self.engine.page.fill('.el-input__inner[placeholder="验证码"]', captcha)
                    return True
                else:
                    print(f"验证码识别失败，第 {attempt + 1} 次尝试")
                    # 刷新验证码
                    await self.engine.page.click('.yzmImg')
                    await asyncio.sleep(1)
            except Exception as e:
                print(f"验证码处理出错: {e}")

        return False

    async def verify_login_success(self) -> bool:
        """验证登录是否成功"""
        try:
            # 等待页面跳转到学习档案页
            await self.engine.page.wait_for_url("**/study/data", timeout=30000)
            return True
        except:
            # 检查是否有错误提示
            error_element = await self.engine.page.query_selector('.el-notification__content p')
            if error_element:
                error_text = await error_element.text_content()
                print(f"登录失败: {error_text}")
            return False
```

### 6.3 状态检查模块设计

#### 6.3.1 用户状态检查器
```python
class StatusChecker:
    """状态检查模块"""

    def __init__(self, study_engine):
        self.engine = study_engine

    async def check_user_status(self) -> dict:
        """检查用户学习状态"""
        # 等待API数据捕获
        await self.engine.page.wait_for_load_state("networkidle")
        await asyncio.sleep(3)

        # 从API捕获器获取数据
        status_data = self.engine.api_capture.get_user_status_data()
        student_data = self.engine.api_capture.get_student_info_data()

        user_status = {
            'name': '',
            'completeStatus': '1',  # 默认未完成
            'onlineTotalCredit': '0',
            'compulsoryCredit': 0,
            'electivesCredit': 0,
            'progress': 0.0
        }

        if status_data:
            user_status.update({
                'completeStatus': status_data.get('completeStatus', '1'),
                'onlineTotalCredit': status_data.get('onlineTotalCredit', '0'),
                'compulsoryCredit': status_data.get('compulsoryCredit', 0),
                'electivesCredit': status_data.get('electivesCredit', 0)
            })

        if student_data:
            user_status['name'] = student_data.get('stuName', '')

        # 计算学习进度
        total_credit = float(user_status['onlineTotalCredit'])
        if total_credit > 0:
            completed_credit = user_status['compulsoryCredit'] + user_status['electivesCredit']
            user_status['progress'] = (completed_credit / total_credit) * 100

        return user_status

    async def is_study_completed(self) -> bool:
        """判断学习是否已完成"""
        status = await self.check_user_status()
        return status.get('completeStatus', '1') == '0'
```

### 6.4 课程学习流程模块设计

#### 6.4.1 课程管理器
```python
class CourseManager:
    """课程管理器"""

    def __init__(self, study_engine):
        self.engine = study_engine

    async def load_or_fetch_courses(self, phone: str) -> dict:
        """加载或获取课程数据"""
        # 检查数据库中是否已有课程数据
        existing_courses = self.get_courses_from_db(phone)

        if self.is_courses_data_valid(existing_courses):
            return existing_courses

        # 重新获取课程数据
        return await self.fetch_fresh_courses()

    async def fetch_fresh_courses(self) -> dict:
        """获取最新的课程数据"""
        # 进入"我的课程"页面
        await self.engine.page.goto("https://study.jxgbwlxy.gov.cn/study/courseMine?id=0")
        await self.engine.page.wait_for_load_state("networkidle")

        # 获取必修课程
        compulsory_courses = await self.fetch_compulsory_courses()

        # 获取选修课程
        elective_courses = await self.fetch_elective_courses()

        # 检查选修课数量是否足够
        required_elective_count = cfg.get(cfg.electiveCourses)
        if len(elective_courses) < required_elective_count:
            await self.add_elective_courses(required_elective_count - len(elective_courses))
            elective_courses = await self.fetch_elective_courses()

        return {
            "必修课": compulsory_courses,
            "选修课": elective_courses
        }

    async def fetch_compulsory_courses(self) -> list:
        """获取必修课程"""
        await self.engine.page.click("text=我的必修课")
        await self.engine.page.wait_for_load_state("networkidle")

        courses = []
        page_num = 1

        while True:
            # 等待API响应并获取数据
            api_data = self.engine.api_capture.get_compulsory_courses_data()

            if api_data and 'records' in api_data:
                for course in api_data['records']:
                    course_info = self.parse_course_data(course)
                    courses.append(course_info)

                # 检查是否有下一页
                if api_data.get('current', 1) < api_data.get('pages', 1):
                    await self.engine.page.click('.btn-next')
                    await self.engine.page.wait_for_load_state("networkidle")
                    page_num += 1
                else:
                    break
            else:
                break

        return courses

    async def fetch_elective_courses(self) -> list:
        """获取选修课程"""
        await self.engine.page.click("text=我的选修课")
        await self.engine.page.wait_for_load_state("networkidle")

        courses = []
        page_num = 1

        while True:
            # 等待API响应并获取数据
            api_data = self.engine.api_capture.get_elective_courses_data()

            if api_data and 'records' in api_data:
                for course in api_data['records']:
                    course_info = self.parse_course_data(course)
                    courses.append(course_info)

                # 检查是否有下一页
                if api_data.get('current', 1) < api_data.get('pages', 1):
                    await self.engine.page.click('.btn-next')
                    await self.engine.page.wait_for_load_state("networkidle")
                    page_num += 1
                else:
                    break
            else:
                break

        return courses

    def parse_course_data(self, course_raw: dict) -> dict:
        """解析课程数据"""
        courseware_id = course_raw.get("courseware", {}).get("id", "")
        return {
            "name": course_raw.get("name", ""),
            "completed": course_raw.get("completed", "0"),
            "id": course_raw.get("id", ""),
            "credit": course_raw.get("credit", 0),
            "percentage": course_raw.get("percentage", "0"),
            "coursewareId": courseware_id,
            "videoUrl": f"https://study.jxgbwlxy.gov.cn/video?id={courseware_id}",
            "completed_date": ""
        }
```

### 6.5 视频监听模块设计

#### 6.5.1 视频学习监控器
```python
class VideoLearningMonitor:
    """视频学习监控器"""

    def __init__(self, study_engine):
        self.engine = study_engine
        self.current_course = None
        self.monitoring = False

    async def start_course_learning(self, course_data: dict) -> bool:
        """开始课程学习"""
        self.current_course = course_data
        video_url = course_data.get("videoUrl", "")

        if not video_url:
            return False

        # 打开视频播放页面
        await self.engine.page.goto(video_url)
        await self.engine.page.wait_for_load_state("networkidle")

        # 处理温馨提示页面
        if "videoChoose" in self.engine.page.url:
            await self.handle_video_choose_page()

        # 更新用户状态
        await self.engine.update_user_status_from_api()

        # 开始监控视频学习
        return await self.monitor_video_learning()

    async def handle_video_choose_page(self):
        """处理温馨提示页面"""
        choose_content = await self.engine.page.query_selector('.choose-content')
        if choose_content:
            await choose_content.click()
            await self.engine.page.wait_for_load_state("networkidle")

    async def monitor_video_learning(self) -> bool:
        """监控视频学习过程"""
        self.monitoring = True

        while self.monitoring:
            try:
                # 检查是否有未完成的课件
                has_incomplete = await self.check_incomplete_courseware()

                if not has_incomplete:
                    # 所有课件已完成
                    await self.mark_course_completed()
                    return True

                # 获取视频播放进度
                video_progress = await self.get_video_progress()
                if video_progress:
                    await self.update_learning_progress(video_progress)

                # 等待一段时间后继续检查
                await asyncio.sleep(30)

            except Exception as e:
                print(f"视频监控出错: {e}")
                await asyncio.sleep(10)

        return False

    async def check_incomplete_courseware(self) -> bool:
        """检查是否有未完成的课件"""
        try:
            has_incomplete = await self.engine.page.evaluate("""
                const elements = document.querySelectorAll('span[title]');
                return Array.from(elements).some(el => el.textContent.includes('未完成'));
            """)
            return has_incomplete
        except:
            return True  # 出错时假设还有未完成的

    async def get_video_progress(self) -> dict:
        """获取视频播放进度"""
        try:
            video_info = await self.engine.page.evaluate("""
                const video = document.querySelector('video');
                if (video) {
                    return {
                        currentTime: video.currentTime,
                        duration: video.duration,
                        paused: video.paused,
                        ended: video.ended
                    };
                }
                return null;
            """)

            if video_info and video_info['duration'] > 0:
                progress = (video_info['currentTime'] / video_info['duration']) * 100
                return {
                    'currentTime': video_info['currentTime'],
                    'duration': video_info['duration'],
                    'progress': progress,
                    'paused': video_info['paused'],
                    'ended': video_info['ended']
                }

            return {}
        except Exception as e:
            print(f"获取视频进度失败: {e}")
            return {}

    async def mark_course_completed(self):
        """标记课程为已完成"""
        if self.current_course:
            self.current_course['completed'] = '1'
            self.current_course['completed_date'] = datetime.now().strftime('%Y-%m-%d')

            # 更新数据库
            # TODO: 实现数据库更新逻辑

    async def update_learning_progress(self, progress_data: dict):
        """更新学习进度"""
        # 发送进度更新信号给UI
        # TODO: 实现进度更新逻辑
        pass

    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
```

## 7. 接口设计

### 6.1 API捕获系统设计

#### 6.1.1 目标API列表
基于simple_api_capture.py的实现，系统需要捕获以下关键API：

```python
TARGET_APIS = [
    "https://study.jxgbwlxy.gov.cn/api/report/myData/online",      # 用户学习状态API
    "https://study.jxgbwlxy.gov.cn/api/study/student/studentArchives/student",  # 学生档案API
    "https://study.jxgbwlxy.gov.cn/api/study/years/yearsCourseware/annualPortalCourseListNew",  # 必修课程API
    "https://study.jxgbwlxy.gov.cn/api/study/my/elective/myElectivesNew"  # 选修课程API
]
```

#### 6.1.2 API数据结构分析

##### 用户学习状态API (`/api/report/myData/online`)
```json
{
  "msg": "操作成功",
  "code": 0,
  "data": {
    "examineStatus": "1",           // 审核状态
    "completeStatus": "0",          // 完成状态
    "onlineTotalCredit": "163.0",   // 在线总学时
    "compulsoryCredit": 37.75,      // 必修学时
    "yearsResponsibilitiesCredits": 0.0,  // 年度责任学时
    "shiftCredit": 0.0,             // 轮班学时
    "onlineAnswerCredits": 0.0,     // 在线答题学时
    "compulsoryZwyCredit": 0.0,     // 必修专业学时
    "electivesCredit": 125.25,      // 选修学时
    "electivesZwyCredit": 0.0,      // 选修专业学时
    "finishShiftNum": 0,            // 完成轮班数
    "zwyCredit": 0.0,               // 专业学时
    "zwyShiftNum": 0,               // 专业轮班数
    "zwyColumnNum": 0               // 专业栏目数
  }
}
```

##### 学生档案API (`/api/study/student/studentArchives/student`)
```json
{
  "msg": "操作成功",
  "code": 0,
  "data": {
    "total": 0,
    "size": 0,
    "current": 0,
    "id": "358125",                 // 学生ID
    "stuName": "帅清明",            // 学生姓名
    "stuStatus": "0",               // 学生状态
    "workRank": "3",                // 工作级别
    "workCompany": {                // 工作单位信息
      "id": "9415",
      "parentIds": "0,1,16,74,8143,171,",
      "name": "萍乡市上栗县桐木镇"
    }
  }
}
```

#### 6.1.3 API捕获管理器设计

```python
class APICapture:
    """API捕获管理器"""

    def __init__(self):
        self.target_apis = [
            "https://study.jxgbwlxy.gov.cn/api/report/myData/online",
            "https://study.jxgbwlxy.gov.cn/api/study/student/studentArchives/student",
            "https://study.jxgbwlxy.gov.cn/api/study/years/yearsCourseware/annualPortalCourseListNew",
            "https://study.jxgbwlxy.gov.cn/api/study/my/elective/myElectivesNew"
        ]
        self.captured_data = {}
        self.callbacks = {}

    def is_target_api(self, url: str) -> bool:
        """判断是否为目标API"""
        return url in self.target_apis

    async def handle_response(self, response):
        """处理API响应"""
        url = response.url

        if self.is_target_api(url):
            try:
                if response.status == 200:
                    data = await response.json()
                    self.captured_data[url] = {
                        'status': response.status,
                        'data': data,
                        'timestamp': time.time()
                    }

                    # 触发回调函数
                    if url in self.callbacks:
                        await self.callbacks[url](data)

            except Exception as e:
                print(f"API捕获失败: {url} - {e}")

    def register_callback(self, api_url: str, callback):
        """注册API回调函数"""
        self.callbacks[api_url] = callback

    def get_user_status_data(self) -> dict:
        """获取用户状态数据"""
        api_url = "https://study.jxgbwlxy.gov.cn/api/report/myData/online"
        if api_url in self.captured_data:
            return self.captured_data[api_url]['data']['data']
        return {}

    def get_student_info_data(self) -> dict:
        """获取学生信息数据"""
        api_url = "https://study.jxgbwlxy.gov.cn/api/study/student/studentArchives/student"
        if api_url in self.captured_data:
            return self.captured_data[api_url]['data']['data']
        return {}

    def get_compulsory_courses_data(self) -> dict:
        """获取必修课程数据"""
        api_url = "https://study.jxgbwlxy.gov.cn/api/study/years/yearsCourseware/annualPortalCourseListNew"
        if api_url in self.captured_data:
            return self.captured_data[api_url]['data']['data']
        return {}

    def get_elective_courses_data(self) -> dict:
        """获取选修课程数据"""
        api_url = "https://study.jxgbwlxy.gov.cn/api/study/my/elective/myElectivesNew"
        if api_url in self.captured_data:
            return self.captured_data[api_url]['data']['data']
        return {}
```

### 6.2 核心接口定义

#### 学习引擎接口
```python
class IStudyEngine(ABC):
    @abstractmethod
    async def login(self, credentials: dict) -> bool:
        """登录接口"""

    @abstractmethod
    async def get_study_status(self) -> dict:
        """获取学习状态"""

    @abstractmethod
    async def start_study(self, course_id: str) -> bool:
        """开始学习"""

    @abstractmethod
    async def capture_api_data(self) -> dict:
        """捕获API数据"""
```

#### OCR识别接口
```python
class IOCREngine(ABC):
    @abstractmethod
    async def recognize(self, image_data: bytes) -> str:
        """识别验证码"""

    @abstractmethod
    def get_confidence(self) -> float:
        """获取识别置信度"""
```

#### 数据访问接口
```python
class IUserDAO(ABC):
    @abstractmethod
    def create_user(self, user_data: dict) -> bool:
        """创建用户"""

    @abstractmethod
    def get_user_by_phone(self, phone: str) -> dict:
        """根据手机号获取用户"""

    @abstractmethod
    def update_user_status(self, phone: str, status: str) -> bool:
        """更新用户状态"""

    @abstractmethod
    def update_user_from_api(self, phone: str, api_data: dict) -> bool:
        """从API数据更新用户信息"""
```

## 7. 部署和维护

### 7.1 依赖管理
```
requirements.txt:
- PySide6
- qfluentwidgets
- playwright
- ddddocr
- baidu-aip
- sqlite3 (内置)
- asyncio (内置)
```

### 7.2 配置文件模板
```json
{
    "System": {
        "AsyncLogin": true,
        "CompulsoryCourses": 20,
        "ElectiveCourses": 20,
        "ConcurrentCount": 2,
        "DelayTime": 1,
        "RetryCount": 3
    },
    "Browser": {
        "BrowserType": "chrome",
        "Headless": false,
        "DisableImages": false,
        "UserAgent": ""
    },
    "OCR": {
        "PrimaryEngine": "ddddocr",
        "BaiduApiKey": "",
        "BaiduSecretKey": "",
        "Timeout": 10
    },
    "API": {
        "Timeout": 30,
        "RetryCount": 3,
        "CaptureEnabled": true,
        "TargetAPIs": [
            "https://study.jxgbwlxy.gov.cn/api/report/myData/online",
            "https://study.jxgbwlxy.gov.cn/api/study/student/studentArchives/student",
            "https://study.jxgbwlxy.gov.cn/api/study/years/yearsCourseware/annualPortalCourseListNew",
            "https://study.jxgbwlxy.gov.cn/api/study/my/elective/myElectivesNew"
        ]
    },
    "Website": {
        "BaseUrl": "https://study.jxgbwlxy.gov.cn",
        "LoginUrl": "https://study.jxgbwlxy.gov.cn/index",
        "VideoUrlPattern": "https://study.jxgbwlxy.gov.cn/video?id={course_id}"
    },
    "Logging": {
        "LogLevel": "INFO",
        "MaxDays": 30,
        "MaxSizeMB": 100
    }
}
```

### 7.3 目录结构规范
```
project_root/
├── app/                 # 应用程序代码
├── data/               # 数据目录
│   ├── config/         # 配置文件
│   ├── database/       # 数据库文件
│   ├── logs/          # 日志文件
│   └── temp/          # 临时文件
├── docs/              # 文档
├── tests/             # 测试代码
└── requirements.txt   # 依赖列表
```

## 8. 总结

本设计文档基于现有的PySide6-Fluent-Widgets脚手架，充分利用其配置系统、界面组件和样式管理等优势，设计了一个功能完整、架构清晰的自动化学习辅助系统。

### 8.1 设计亮点
1. **完整学习流程**: 从登录到课程完成的全自动化流程
2. **模块化设计**: 学习工具和OCR识别独立开发，便于维护
3. **配置驱动**: 所有功能都可通过配置文件控制
4. **异步并发**: 支持多账号并行学习
5. **容错机制**: 完善的异常处理和重试机制
6. **用户友好**: 基于Fluent Design的现代化界面

### 8.2 技术特色
1. **智能学习流程**: 完整的自动登录→状态检查→课程学习→视频监控流程
2. **智能API捕获**: 基于Playwright的实时API数据捕获，支持多个关键API
3. **双引擎OCR**: Ddddocr + 百度OCR备份方案，提高验证码识别成功率
4. **智能并发**: 异步登录避免OCR冲突，支持多账号并行学习
5. **实时监控**: 学习进度实时更新，视频播放状态监控
6. **自动课程管理**: 智能获取必修课/选修课，自动添加不足的选修课
7. **数据持久化**: SQLite3本地数据存储，完整的用户和课程数据管理
8. **日志追踪**: 完整的操作日志记录，便于问题排查

### 8.3 扩展性考虑
1. **可配置API列表**: 支持动态添加新的目标API
2. **插件化OCR**: 易于添加新的OCR引擎
3. **配置化选择器**: 网站结构变更时易于适配
4. **模块化界面**: 新功能界面易于集成
5. **标准化接口**: 便于功能扩展和测试
6. **API数据处理**: 支持自定义API数据处理逻辑

请确认此设计方案是否符合您的需求，我将根据您的反馈进行调整，然后开始具体的开发实现。